/**
 ******************************************************************************
 * @file           : gauge_test.h
 * @brief          : 双仪表盘测试程序头文件
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 仪表盘功能测试和演示程序
 ******************************************************************************
 */

#ifndef __GAUGE_TEST_H
#define __GAUGE_TEST_H

#include "main.h"

/* 函数声明 */
void gauge_simple_test(void);           // 简单仪表盘测试
void gauge_animation_test(void);        // 仪表盘动画测试
void gauge_random_test(void);           // 仪表盘随机测试
void gauge_performance_test(void);      // 仪表盘性能测试
void gauge_comprehensive_test(void);    // 仪表盘综合测试
void gauge_individual_test(void);       // 仪表盘单独更新测试
void gauge_boundary_test(void);         // 仪表盘边界值测试
void gauge_full_demo(void);             // 仪表盘完整演示

#endif /* __GAUGE_TEST_H */
