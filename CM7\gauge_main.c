/**
 ******************************************************************************
 * @file           : gauge_main.c
 * @brief          : 双仪表盘主程序
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 双仪表盘演示程序主文件
 ******************************************************************************
 */

#include "main.h"
#include <stdio.h>
#include <math.h>

/* 仪表盘配置 */
#define GAUGE_RADIUS        60      // 仪表盘半径
#define GAUGE_CENTER_Y      200     // 仪表盘Y中心坐标
#define GAUGE_LEFT_X        160     // 左侧仪表盘X中心坐标  
#define GAUGE_RIGHT_X       480     // 右侧仪表盘X中心坐标
#define GAUGE_POINTER_LEN   50      // 指针长度

/* 角度范围 */
#define ANGLE_MIN          -135     // 起始角度(-135度)
#define ANGLE_MAX           135     // 终止角度(135度)
#define ANGLE_RANGE         270     // 角度范围(270度)

/* 数学常量 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 颜色定义 */
#define WHITE   0xFFFF
#define BLACK   0x0000
#define RED     0xF800
#define GREEN   0x07E0
#define BLUE    0x001F
#define YELLOW  0xFFE0

/* 外部函数声明 */
extern void SystemClock_Config(void);
extern void Error_Handler(void);
extern void MPU_Config(void);

/* 简化的LCD函数 - 基于内存映射 */
#define LCD_BASE    ((uint32_t)(0x60000000 | 0x0007FFFE))
#define LCD         ((LCD_TypeDef *) LCD_BASE)

typedef struct
{
    __IO uint16_t LCD_REG;
    __IO uint16_t LCD_RAM;
} LCD_TypeDef;

/* LCD基础函数 */
void lcd_write_reg(uint16_t regval)
{
    LCD->LCD_REG = regval;
}

void lcd_write_data(uint16_t data)
{
    LCD->LCD_RAM = data;
}

uint16_t lcd_read_data(void)
{
    return LCD->LCD_RAM;
}

void lcd_write_reg_data(uint16_t reg, uint16_t data)
{
    lcd_write_reg(reg);
    lcd_write_data(data);
}

/* 简化的绘图函数 */
void simple_lcd_clear(uint16_t color)
{
    uint32_t i;
    
    lcd_write_reg(0x2A);    // Column Address Set
    lcd_write_data(0);
    lcd_write_data(0);
    lcd_write_data(1);
    lcd_write_data(0xDF);   // 479
    
    lcd_write_reg(0x2B);    // Page Address Set  
    lcd_write_data(0);
    lcd_write_data(0);
    lcd_write_data(1);
    lcd_write_data(0x3F);   // 319
    
    lcd_write_reg(0x2C);    // Memory Write
    
    for(i = 0; i < 480 * 320; i++) {
        lcd_write_data(color);
    }
}

void simple_lcd_draw_point(uint16_t x, uint16_t y, uint16_t color)
{
    lcd_write_reg(0x2A);    // Column Address Set
    lcd_write_data(x >> 8);
    lcd_write_data(x & 0xFF);
    lcd_write_data(x >> 8);
    lcd_write_data(x & 0xFF);
    
    lcd_write_reg(0x2B);    // Page Address Set
    lcd_write_data(y >> 8);
    lcd_write_data(y & 0xFF);
    lcd_write_data(y >> 8);
    lcd_write_data(y & 0xFF);
    
    lcd_write_reg(0x2C);    // Memory Write
    lcd_write_data(color);
}

void simple_lcd_draw_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    int16_t dx = abs(x2 - x1);
    int16_t dy = abs(y2 - y1);
    int16_t sx = (x1 < x2) ? 1 : -1;
    int16_t sy = (y1 < y2) ? 1 : -1;
    int16_t err = dx - dy;
    
    while(1) {
        simple_lcd_draw_point(x1, y1, color);
        
        if(x1 == x2 && y1 == y2) break;
        
        int16_t e2 = 2 * err;
        if(e2 > -dy) {
            err -= dy;
            x1 += sx;
        }
        if(e2 < dx) {
            err += dx;
            y1 += sy;
        }
    }
}

void simple_lcd_draw_circle(uint16_t x0, uint16_t y0, uint16_t r, uint16_t color)
{
    int16_t x = r;
    int16_t y = 0;
    int16_t err = 0;
    
    while(x >= y) {
        simple_lcd_draw_point(x0 + x, y0 + y, color);
        simple_lcd_draw_point(x0 + y, y0 + x, color);
        simple_lcd_draw_point(x0 - y, y0 + x, color);
        simple_lcd_draw_point(x0 - x, y0 + y, color);
        simple_lcd_draw_point(x0 - x, y0 - y, color);
        simple_lcd_draw_point(x0 - y, y0 - x, color);
        simple_lcd_draw_point(x0 + y, y0 - x, color);
        simple_lcd_draw_point(x0 + x, y0 - y, color);
        
        if(err <= 0) {
            y += 1;
            err += 2*y + 1;
        }
        if(err > 0) {
            x -= 1;
            err -= 2*x + 1;
        }
    }
}

void simple_lcd_fill_circle(uint16_t x0, uint16_t y0, uint16_t r, uint16_t color)
{
    for(int16_t y = -r; y <= r; y++) {
        for(int16_t x = -r; x <= r; x++) {
            if(x*x + y*y <= r*r) {
                simple_lcd_draw_point(x0 + x, y0 + y, color);
            }
        }
    }
}

/* 仪表盘函数 */
void draw_gauge_scales(uint16_t center_x, uint16_t center_y)
{
    int16_t angle;
    float rad;
    int16_t x1, y1, x2, y2;
    
    for(angle = ANGLE_MIN; angle <= ANGLE_MAX; angle += 30) {
        rad = angle * M_PI / 180.0f;
        
        x1 = center_x + (int16_t)(cosf(rad) * (GAUGE_RADIUS - 5));
        y1 = center_y + (int16_t)(sinf(rad) * (GAUGE_RADIUS - 5));
        
        x2 = center_x + (int16_t)(cosf(rad) * (GAUGE_RADIUS - 15));
        y2 = center_y + (int16_t)(sinf(rad) * (GAUGE_RADIUS - 15));
        
        simple_lcd_draw_line(x1, y1, x2, y2, WHITE);
    }
}

void draw_gauge_pointer(uint16_t center_x, uint16_t center_y, uint16_t value)
{
    if(value > 100) value = 100;
    
    float angle = ANGLE_MIN + (ANGLE_RANGE * value) / 100.0f;
    float rad = angle * M_PI / 180.0f;
    
    int16_t x_end = center_x + (int16_t)(cosf(rad) * GAUGE_POINTER_LEN);
    int16_t y_end = center_y + (int16_t)(sinf(rad) * GAUGE_POINTER_LEN);
    
    simple_lcd_draw_line(center_x, center_y, x_end, y_end, RED);
    simple_lcd_fill_circle(center_x, center_y, 5, BLUE);
}

void clear_gauge_pointer(uint16_t center_x, uint16_t center_y)
{
    simple_lcd_fill_circle(center_x, center_y, GAUGE_POINTER_LEN + 5, BLACK);
    simple_lcd_draw_circle(center_x, center_y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(center_x, center_y);
}

void update_dual_gauges(uint16_t left_value, uint16_t right_value)
{
    clear_gauge_pointer(GAUGE_LEFT_X, GAUGE_CENTER_Y);
    draw_gauge_pointer(GAUGE_LEFT_X, GAUGE_CENTER_Y, left_value);
    
    clear_gauge_pointer(GAUGE_RIGHT_X, GAUGE_CENTER_Y);
    draw_gauge_pointer(GAUGE_RIGHT_X, GAUGE_CENTER_Y, right_value);
}

void init_dual_gauges(void)
{
    simple_lcd_clear(BLACK);
    
    simple_lcd_draw_circle(GAUGE_LEFT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(GAUGE_LEFT_X, GAUGE_CENTER_Y);
    
    simple_lcd_draw_circle(GAUGE_RIGHT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(GAUGE_RIGHT_X, GAUGE_CENTER_Y);
    
    printf("双仪表盘初始化完成\r\n");
}

/**
 * @brief  主函数
 * @retval int
 */
int main(void)
{
    int32_t timeout;
    
    /* MPU Configuration */
    MPU_Config();
    
    /* Wait until CPU2 boots */
    timeout = 0xFFFF;
    while((__HAL_RCC_GET_FLAG(RCC_FLAG_D2CKRDY) != RESET) && (timeout-- > 0));
    if(timeout < 0) {
        Error_Handler();
    }
    
    /* MCU Configuration */
    HAL_Init();
    SystemClock_Config();
    
    /* Release CPU2 */
    __HAL_RCC_HSEM_CLK_ENABLE();
    HAL_HSEM_FastTake(0);
    HAL_HSEM_Release(0, 0);
    
    timeout = 0xFFFF;
    while((__HAL_RCC_GET_FLAG(RCC_FLAG_D2CKRDY) == RESET) && (timeout-- > 0));
    if(timeout < 0) {
        Error_Handler();
    }
    
    printf("STM32H745 双仪表盘演示程序启动\r\n");
    
    /* 初始化双仪表盘 */
    init_dual_gauges();
    
    /* 演示循环 */
    uint16_t speed = 0, rpm = 0;
    uint8_t speed_dir = 0, rpm_dir = 0;
    
    while(1) {
        /* 更新数值 */
        if(speed_dir == 0) {
            if(speed < 100) speed += 2;
            else speed_dir = 1;
        } else {
            if(speed > 0) speed -= 2;
            else speed_dir = 0;
        }
        
        if(rpm_dir == 0) {
            if(rpm < 100) rpm += 3;
            else rpm_dir = 1;
        } else {
            if(rpm > 0) rpm -= 3;
            else rpm_dir = 0;
        }
        
        /* 更新仪表盘 */
        update_dual_gauges(speed, rpm);
        
        HAL_Delay(150);
    }
}
