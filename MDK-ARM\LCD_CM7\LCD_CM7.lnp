--cpu Cortex-M7.fp.dp
"lcd_cm7\startup_stm32h745xx_cm7.o"
"lcd_cm7\main_1.o"
"lcd_cm7\stm32h7xx_it_1.o"
"lcd_cm7\stm32h7xx_hal_msp_1.o"
"lcd_cm7\stm32h7xx_hal_cortex.o"
"lcd_cm7\stm32h7xx_hal_tim.o"
"lcd_cm7\stm32h7xx_hal_tim_ex.o"
"lcd_cm7\stm32h7xx_hal_rcc.o"
"lcd_cm7\stm32h7xx_hal_rcc_ex.o"
"lcd_cm7\stm32h7xx_hal_flash.o"
"lcd_cm7\stm32h7xx_hal_flash_ex.o"
"lcd_cm7\stm32h7xx_hal_gpio.o"
"lcd_cm7\stm32h7xx_hal_hsem.o"
"lcd_cm7\stm32h7xx_hal_dma.o"
"lcd_cm7\stm32h7xx_hal_dma_ex.o"
"lcd_cm7\stm32h7xx_hal_mdma.o"
"lcd_cm7\stm32h7xx_hal_pwr.o"
"lcd_cm7\stm32h7xx_hal_pwr_ex.o"
"lcd_cm7\stm32h7xx_hal.o"
"lcd_cm7\stm32h7xx_hal_i2c.o"
"lcd_cm7\stm32h7xx_hal_i2c_ex.o"
"lcd_cm7\stm32h7xx_hal_exti.o"
"lcd_cm7\system_stm32h7xx_dualcore_boot_cm4_cm7.o"
--strict --scatter "stm32h745xx_flash_CM7.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "LCD_CM7.map" -o LCD_CM7\LCD_CM7.axf