/**
 ******************************************************************************
 * @file           : gauge.c
 * @brief          : 双仪表盘显示实现
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 适用于STM32H745 LCD驱动，支持双仪表盘显示
 ******************************************************************************
 */

#include "gauge.h"
#include <stdio.h>

/* 数学常量定义 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 全局仪表盘对象 */
static gauge_t gauge_left = {GAUGE_LEFT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, 0, 100, "Speed", "km/h"};
static gauge_t gauge_right = {GAUGE_RIGHT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, 0, 100, "RPM", "x100"};

/**
 * @brief       角度转弧度
 * @param       degrees: 角度值
 * @retval      弧度值
 */
float gauge_deg_to_rad(float degrees)
{
    return degrees * M_PI / 180.0f;
}

/**
 * @brief       整数余弦函数(放大1000倍)
 * @param       angle_rad: 弧度值
 * @retval      余弦值*1000
 */
int16_t gauge_cos_int(float angle_rad)
{
    return (int16_t)(cosf(angle_rad) * 1000);
}

/**
 * @brief       整数正弦函数(放大1000倍)
 * @param       angle_rad: 弧度值
 * @retval      正弦值*1000
 */
int16_t gauge_sin_int(float angle_rad)
{
    return (int16_t)(sinf(angle_rad) * 1000);
}

/**
 * @brief       初始化双仪表盘
 * @param       无
 * @retval      无
 */
void gauge_init(void)
{
    /* 确保LCD已初始化 */
    if(lcddev.id == 0) {
        lcd_init();
    }
    
    /* 设置横屏显示 */
    lcd_display_dir(1);
    
    /* 绘制背景 */
    gauge_draw_background();
    
    printf("双仪表盘初始化完成\r\n");
}

/**
 * @brief       绘制背景
 * @param       无
 * @retval      无
 */
void gauge_draw_background(void)
{
    /* 清屏为黑色 */
    lcd_clear(GAUGE_BG_COLOR);
    
    /* 绘制左侧仪表盘外圈和刻度 */
    lcd_draw_circle(gauge_left.center_x, gauge_left.center_y, gauge_left.radius, GAUGE_RING_COLOR);
    gauge_draw_scales(gauge_left.center_x, gauge_left.center_y);
    
    /* 绘制右侧仪表盘外圈和刻度 */
    lcd_draw_circle(gauge_right.center_x, gauge_right.center_y, gauge_right.radius, GAUGE_RING_COLOR);
    gauge_draw_scales(gauge_right.center_x, gauge_right.center_y);
    
    /* 绘制标题 */
    lcd_show_string(gauge_left.center_x - 30, gauge_left.center_y - 120, 60, 16, 16, gauge_left.title, GAUGE_TEXT_COLOR);
    lcd_show_string(gauge_right.center_x - 30, gauge_right.center_y - 120, 60, 16, 16, gauge_right.title, GAUGE_TEXT_COLOR);
    
    printf("仪表盘背景绘制完成\r\n");
}

/**
 * @brief       绘制刻度
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标
 * @retval      无
 */
void gauge_draw_scales(uint16_t center_x, uint16_t center_y)
{
    int16_t angle;
    float rad;
    int16_t x1, y1, x2, y2;
    
    /* 绘制主刻度(每30度一个) */
    for(angle = ANGLE_MIN; angle <= ANGLE_MAX; angle += 30) {
        rad = gauge_deg_to_rad(angle);
        
        /* 外圈点 */
        x1 = center_x + (gauge_cos_int(rad) * (GAUGE_RADIUS - 5)) / 1000;
        y1 = center_y + (gauge_sin_int(rad) * (GAUGE_RADIUS - 5)) / 1000;
        
        /* 内圈点 */
        x2 = center_x + (gauge_cos_int(rad) * (GAUGE_RADIUS - 15)) / 1000;
        y2 = center_y + (gauge_sin_int(rad) * (GAUGE_RADIUS - 15)) / 1000;
        
        lcd_draw_line(x1, y1, x2, y2, GAUGE_SCALE_COLOR);
    }
    
    /* 绘制次刻度(每15度一个) */
    for(angle = ANGLE_MIN + 15; angle < ANGLE_MAX; angle += 30) {
        rad = gauge_deg_to_rad(angle);
        
        /* 外圈点 */
        x1 = center_x + (gauge_cos_int(rad) * (GAUGE_RADIUS - 5)) / 1000;
        y1 = center_y + (gauge_sin_int(rad) * (GAUGE_RADIUS - 5)) / 1000;
        
        /* 内圈点 */
        x2 = center_x + (gauge_cos_int(rad) * (GAUGE_RADIUS - 10)) / 1000;
        y2 = center_y + (gauge_sin_int(rad) * (GAUGE_RADIUS - 10)) / 1000;
        
        lcd_draw_line(x1, y1, x2, y2, GAUGE_SCALE_COLOR);
    }
}

/**
 * @brief       清除指针区域
 * @param       pos: 仪表盘位置
 * @retval      无
 */
void gauge_clear_pointer_area(gauge_position_t pos)
{
    uint16_t center_x = (pos == GAUGE_LEFT) ? gauge_left.center_x : gauge_right.center_x;
    uint16_t center_y = (pos == GAUGE_LEFT) ? gauge_left.center_y : gauge_right.center_y;
    
    /* 填充指针活动区域为背景色 */
    lcd_fill_circle(center_x, center_y, GAUGE_POINTER_LEN + 5, GAUGE_BG_COLOR);
    
    /* 重新绘制刻度 */
    gauge_draw_scales(center_x, center_y);
}

/**
 * @brief       绘制指针
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标  
 * @param       value: 数值(0-100)
 * @retval      无
 */
void gauge_draw_pointer(uint16_t center_x, uint16_t center_y, uint16_t value)
{
    /* 限制数值范围 */
    if(value > 100) value = 100;
    
    /* 计算指针角度 */
    float angle = ANGLE_MIN + (ANGLE_RANGE * value) / 100.0f;
    float rad = gauge_deg_to_rad(angle);
    
    /* 计算指针端点 */
    int16_t x_end = center_x + (gauge_cos_int(rad) * GAUGE_POINTER_LEN) / 1000;
    int16_t y_end = center_y + (gauge_sin_int(rad) * GAUGE_POINTER_LEN) / 1000;
    
    /* 计算指针尾部 */
    int16_t x_tail = center_x - (gauge_cos_int(rad) * GAUGE_TAIL_LEN) / 1000;
    int16_t y_tail = center_y - (gauge_sin_int(rad) * GAUGE_TAIL_LEN) / 1000;
    
    /* 绘制指针主体 */
    lcd_draw_line(center_x, center_y, x_end, y_end, GAUGE_POINTER_COLOR);
    
    /* 绘制指针尾部 */
    lcd_draw_line(center_x, center_y, x_tail, y_tail, GAUGE_POINTER_COLOR);
    
    /* 绘制中心圆 */
    lcd_fill_circle(center_x, center_y, GAUGE_CENTER_R, GAUGE_CENTER_COLOR);
}

/**
 * @brief       绘制文字信息
 * @param       pos: 仪表盘位置
 * @param       value: 数值
 * @retval      无
 */
void gauge_draw_text(gauge_position_t pos, uint16_t value)
{
    char str[16];
    uint16_t center_x = (pos == GAUGE_LEFT) ? gauge_left.center_x : gauge_right.center_x;
    uint16_t center_y = (pos == GAUGE_LEFT) ? gauge_left.center_y : gauge_right.center_y;
    char *unit = (pos == GAUGE_LEFT) ? gauge_left.unit : gauge_right.unit;
    
    /* 清除旧数字 */
    lcd_fill(center_x - 30, center_y + 30, center_x + 30, center_y + 50, GAUGE_BG_COLOR);
    
    /* 显示数值 */
    sprintf(str, "%d %s", value, unit);
    lcd_show_string(center_x - 25, center_y + 35, 50, 16, 16, str, GAUGE_TEXT_COLOR);
}

/**
 * @brief       绘制单个仪表盘
 * @param       pos: 仪表盘位置
 * @param       value: 数值(0-100)
 * @retval      无
 */
void gauge_draw_single(gauge_position_t pos, uint16_t value)
{
    uint16_t center_x = (pos == GAUGE_LEFT) ? gauge_left.center_x : gauge_right.center_x;
    uint16_t center_y = (pos == GAUGE_LEFT) ? gauge_left.center_y : gauge_right.center_y;
    
    /* 清除指针区域 */
    gauge_clear_pointer_area(pos);
    
    /* 绘制新指针 */
    gauge_draw_pointer(center_x, center_y, value);
    
    /* 更新数字显示 */
    gauge_draw_text(pos, value);
    
    /* 更新存储值 */
    if(pos == GAUGE_LEFT) {
        gauge_left.value = value;
    } else {
        gauge_right.value = value;
    }
}

/**
 * @brief       绘制双仪表盘
 * @param       left_value: 左侧数值(0-100)
 * @param       right_value: 右侧数值(0-100)
 * @retval      无
 */
void gauge_draw_both(uint16_t left_value, uint16_t right_value)
{
    gauge_draw_single(GAUGE_LEFT, left_value);
    gauge_draw_single(GAUGE_RIGHT, right_value);
}

/**
 * @brief       更新仪表盘数值
 * @param       pos: 仪表盘位置
 * @param       value: 新数值(0-100)
 * @retval      无
 */
void gauge_update_value(gauge_position_t pos, uint16_t value)
{
    gauge_draw_single(pos, value);
}

/**
 * @brief       仪表盘演示
 * @param       无
 * @retval      无
 */
void gauge_demo(void)
{
    uint16_t speed = 0, rpm = 0;
    uint8_t speed_dir = 0, rpm_dir = 0;
    
    printf("开始仪表盘演示...\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 演示循环 */
    for(uint16_t i = 0; i < 500; i++) {
        /* 更新速度值 */
        if(speed_dir == 0) {
            if(speed < 100) speed += 2;
            else speed_dir = 1;
        } else {
            if(speed > 0) speed -= 2;
            else speed_dir = 0;
        }
        
        /* 更新转速值(不同步变化) */
        if(rpm_dir == 0) {
            if(rpm < 100) rpm += 3;
            else rpm_dir = 1;
        } else {
            if(rpm > 0) rpm -= 3;
            else rpm_dir = 0;
        }
        
        /* 绘制双仪表盘 */
        gauge_draw_both(speed, rpm);
        
        /* 延时 */
        HAL_Delay(100);
        
        /* 每50次循环输出一次状态 */
        if(i % 50 == 0) {
            printf("Speed: %d, RPM: %d\r\n", speed, rpm);
        }
    }
    
    printf("仪表盘演示完成\r\n");
}
