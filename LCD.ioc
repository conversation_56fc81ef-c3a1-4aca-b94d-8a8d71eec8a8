#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.IPParameters=default_mode_Activation
CORTEX_M7.default_mode_Activation=1
CortexM4.IPs=CORTEX_M4\:I,DEBUG,FATFS_M4\:I,FREERTOS_M4\:I,IWDG2\:I,OPENAMP_M4\:I,PDM2PCM_M4\:I,PWR,RCC,RESMGR_UTILITY,SYS_M4\:I,USB_DEVICE_M4\:I,USB_HOST_M4\:I,VREFBUF,WWDG2\:I,GPIO,DMA,BDMA,MDMA,NVIC2\:I,Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0_M4\:I
CortexM7.IPs=CORTEX_M7\:I,DEBUG\:I,FATFS_M7\:I,FREERTOS_M7\:I,IWDG1\:I,OPENAMP_M7\:I,PDM2PCM_M7\:I,PWR\:I,RCC\:I,RESMGR_UTILITY\:I,SYS\:I,USB_DEVICE_M7\:I,USB_HOST_M7\:I,VREFBUF\:I,WWDG1\:I,GPIO\:I,DMA\:I,BDMA\:I,MDMA\:I,NVIC1\:I,LTDC\:I,DMA2D\:I,ADC1\:I,ADC2\:I,TIM6\:I,FMC\:I,QUADSPI\:I,JPEG\:I,CRC\:I
File.Version=6
GPIO.groupedBy=
Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0_M4_SwParameter=ConnectivityCcWirelessJjWifiJjmqtt\:true;
KeepUserPlacement=false
Mcu.CPN=STM32H745XIH6
Mcu.Context0=CortexM7
Mcu.Context1=CortexM4
Mcu.ContextNb=2
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M4
Mcu.IP1=CORTEX_M7
Mcu.IP2=NVIC1
Mcu.IP3=NVIC2
Mcu.IP4=RCC
Mcu.IP5=SYS_M4
Mcu.IP6=SYS
Mcu.IPNb=7
Mcu.Name=STM32H745XIHx
Mcu.Package=TFBGA240
Mcu.Pin0=VP_SYS_VS_Systick
Mcu.Pin1=VP_SYS_M4_VS_Systick
Mcu.PinsNb=2
Mcu.ThirdParty0=Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0
Mcu.ThirdParty0_ContextShortName=M4
Mcu.ThirdParty0_Instance=Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0_M4
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32H745XIHx
MxCube.Version=6.11.1
MxDb.Version=DB.6.0.111
NVIC1.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.ForceEnableDMAVector=true
NVIC1.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC1.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC1.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC1.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.ForceEnableDMAVector=true
NVIC2.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC2.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC2.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC2.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PinOutPanel.CurrentBGAView=Top
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.BootMode=boot0
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H745XIHx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=M4-0x200,M7-0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=LCD.ioc
ProjectManager.ProjectName=LCD
ProjectManager.ProjectStructure=M7\:CortexM7 Project\:true;M4\:CortexM4 Project\:true;
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=M4-0x400,M7-0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false-CortexM7,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true-CortexM7,0-MX_CORTEX_M4_Init-CORTEX_M4-false-HAL-true-CortexM4
RCC.ADCFreq_Value=*********
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CPU2Freq_Value=*********
RCC.CPU2SystikFreq_Value=*********
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=4
RCC.DIVN1=60
RCC.DIVP1Freq_Value=*********
RCC.DIVP2Freq_Value=*********
RCC.DIVP3Freq_Value=*********
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=*********
RCC.DIVQ3Freq_Value=*********
RCC.DIVR1Freq_Value=*********
RCC.DIVR2Freq_Value=*********
RCC.DIVR3Freq_Value=*********
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMFreq_Value=*********
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CPU2Freq_Value,CPU2SystikFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVN1,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMFreq_Value,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL2FRACN,PLL3FRACN,PLLFRACN,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=*********
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL3FRACN=0
RCC.PLLFRACN=0
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=16000000
RCC.VCOInput2Freq_Value=2000000
RCC.VCOInput3Freq_Value=2000000
SYS.userName=SYS_M7
VP_SYS_M4_VS_Systick.Mode=SysTick
VP_SYS_M4_VS_Systick.Signal=SYS_M4_VS_Systick
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
