# STM32H745 LCD项目编译和下载脚本
# 使用方法: .\build_and_flash.ps1

Write-Host "=== STM32H745 LCD项目编译和下载工具 ===" -ForegroundColor Green

# 检查STM32CubeProgrammer是否可用
try {
    $programmer = Get-Command STM32_Programmer_CLI -ErrorAction Stop
    Write-Host "✓ 找到STM32CubeProgrammer: $($programmer.Source)" -ForegroundColor Green
} catch {
    Write-Host "✗ 未找到STM32CubeProgrammer，请安装STM32CubeCLT" -ForegroundColor Red
    exit 1
}

# 检查是否有预编译的hex文件
$hexFile = "MDK-ARM\LCD_CM7\LCD_CM7.hex"
if (Test-Path $hexFile) {
    Write-Host "✓ 找到预编译的hex文件: $hexFile" -ForegroundColor Green
    
    # 检查连接的调试器
    Write-Host "`n检查连接的调试器..." -ForegroundColor Yellow
    $devices = STM32_Programmer_CLI -l
    
    if ($devices -match "ST-Link") {
        Write-Host "✓ 检测到ST-Link调试器" -ForegroundColor Green
        
        # 下载程序到开发板
        Write-Host "`n开始下载程序到开发板..." -ForegroundColor Yellow
        $result = STM32_Programmer_CLI -c port=SWD -w $hexFile -v -rst
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 程序下载成功！" -ForegroundColor Green
            Write-Host "LCD测试程序已下载到STM32H745开发板" -ForegroundColor Cyan
        } else {
            Write-Host "✗ 程序下载失败" -ForegroundColor Red
        }
    } elseif ($devices -match "J-Link") {
        Write-Host "✓ 检测到J-Link调试器" -ForegroundColor Green
        Write-Host "请使用J-Link工具下载hex文件: $hexFile" -ForegroundColor Yellow
    } else {
        Write-Host "✗ 未检测到调试器，请检查连接" -ForegroundColor Red
        Write-Host "支持的调试器: ST-Link, J-Link" -ForegroundColor Yellow
        Write-Host "可用的串口:" -ForegroundColor Yellow
        STM32_Programmer_CLI -l | Select-String "Port:"
    }
} else {
    Write-Host "✗ 未找到hex文件，需要先编译项目" -ForegroundColor Red
    
    # 检查编译环境
    Write-Host "`n检查编译环境..." -ForegroundColor Yellow
    
    # 检查Keil MDK
    $keilPath = Get-ChildItem "C:\Keil*" -Directory -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($keilPath) {
        Write-Host "✓ 找到Keil MDK: $($keilPath.FullName)" -ForegroundColor Green
        Write-Host "请使用Keil MDK打开项目文件: MDK-ARM\LCD.uvprojx" -ForegroundColor Cyan
    }
    
    # 检查IAR EWARM
    $iarPath = Get-ChildItem "C:\Program Files*\IAR Systems" -Directory -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($iarPath) {
        Write-Host "✓ 找到IAR EWARM: $($iarPath.FullName)" -ForegroundColor Green
        Write-Host "请使用IAR EWARM打开工作空间: EWARM\Project.eww" -ForegroundColor Cyan
    }
    
    # 检查STM32CubeIDE
    $cubeIdePath = Get-ChildItem "C:\ST\STM32CubeIDE*" -Directory -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($cubeIdePath) {
        Write-Host "✓ 找到STM32CubeIDE: $($cubeIdePath.FullName)" -ForegroundColor Green
        Write-Host "请使用STM32CubeIDE导入项目进行编译" -ForegroundColor Cyan
    }
    
    if (-not ($keilPath -or $iarPath -or $cubeIdePath)) {
        Write-Host "✗ 未找到支持的IDE，请安装以下任一工具:" -ForegroundColor Red
        Write-Host "  - Keil MDK-ARM" -ForegroundColor Yellow
        Write-Host "  - IAR EWARM" -ForegroundColor Yellow
        Write-Host "  - STM32CubeIDE" -ForegroundColor Yellow
    }
}

Write-Host "`n=== 使用说明 ===" -ForegroundColor Cyan
Write-Host "1. 连接STM32H745开发板到电脑" -ForegroundColor White
Write-Host "2. 确保ST-Link或J-Link调试器正常连接" -ForegroundColor White
Write-Host "3. 运行此脚本进行下载" -ForegroundColor White
Write-Host "4. 下载完成后，LCD将显示测试画面" -ForegroundColor White

Write-Host "`n=== 测试功能 ===" -ForegroundColor Cyan
Write-Host "- LCD初始化和ID检测" -ForegroundColor White
Write-Host "- 基础绘图功能（点、线、矩形、圆）" -ForegroundColor White
Write-Host "- 填充功能（矩形填充、圆形填充）" -ForegroundColor White
Write-Host "- 字符显示功能（字符、数字、字符串）" -ForegroundColor White
Write-Host "- 多种字体大小支持（12/16/24/32）" -ForegroundColor White

Read-Host "`n按回车键退出"
