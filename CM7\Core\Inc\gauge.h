/**
 ******************************************************************************
 * @file           : gauge.h
 * @brief          : 双仪表盘显示头文件
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 适用于STM32H745 LCD驱动，支持双仪表盘显示
 ******************************************************************************
 */

#ifndef __GAUGE_H
#define __GAUGE_H

#include "lcd.h"
#include <math.h>

/* 仪表盘配置参数 */
#define GAUGE_RADIUS        80      // 仪表盘半径
#define GAUGE_CENTER_Y      240     // 仪表盘Y中心坐标
#define GAUGE_LEFT_X        200     // 左侧仪表盘X中心坐标  
#define GAUGE_RIGHT_X       600     // 右侧仪表盘X中心坐标
#define GAUGE_POINTER_LEN   65      // 指针长度
#define GAUGE_TAIL_LEN      20      // 指针尾部长度
#define GAUGE_CENTER_R      8       // 中心圆半径

/* 角度范围定义 */
#define ANGLE_MIN          -135     // 起始角度(-135度)
#define ANGLE_MAX           135     // 终止角度(135度)
#define ANGLE_RANGE         270     // 角度范围(270度)

/* 仪表盘颜色定义 */
#define GAUGE_BG_COLOR      BLACK   // 背景色
#define GAUGE_RING_COLOR    WHITE   // 外圈颜色
#define GAUGE_SCALE_COLOR   LGRAY   // 刻度颜色
#define GAUGE_POINTER_COLOR RED     // 指针颜色
#define GAUGE_CENTER_COLOR  DARKBLUE // 中心点颜色
#define GAUGE_TEXT_COLOR    YELLOW  // 文字颜色

/* 仪表盘类型枚举 */
typedef enum {
    GAUGE_LEFT = 0,     // 左侧仪表盘
    GAUGE_RIGHT = 1     // 右侧仪表盘
} gauge_position_t;

/* 仪表盘结构体 */
typedef struct {
    uint16_t center_x;      // 中心X坐标
    uint16_t center_y;      // 中心Y坐标
    uint8_t radius;         // 半径
    uint16_t value;         // 当前值(0-100)
    uint16_t max_value;     // 最大值
    char title[16];         // 标题
    char unit[8];           // 单位
} gauge_t;

/* 函数声明 */
void gauge_init(void);                                          // 初始化双仪表盘
void gauge_draw_background(void);                               // 绘制背景
void gauge_draw_single(gauge_position_t pos, uint16_t value);   // 绘制单个仪表盘
void gauge_draw_both(uint16_t left_value, uint16_t right_value); // 绘制双仪表盘
void gauge_update_value(gauge_position_t pos, uint16_t value);  // 更新仪表盘数值
void gauge_demo(void);                                          // 仪表盘演示
void gauge_clear_pointer_area(gauge_position_t pos);           // 清除指针区域
void gauge_draw_scales(uint16_t center_x, uint16_t center_y);   // 绘制刻度
void gauge_draw_pointer(uint16_t center_x, uint16_t center_y, uint16_t value); // 绘制指针
void gauge_draw_text(gauge_position_t pos, uint16_t value);     // 绘制文字信息

/* 数学辅助函数 */
float gauge_deg_to_rad(float degrees);                         // 角度转弧度
int16_t gauge_cos_int(float angle_rad);                        // 整数余弦
int16_t gauge_sin_int(float angle_rad);                        // 整数正弦

#endif /* __GAUGE_H */
