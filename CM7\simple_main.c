/**
 ******************************************************************************
 * @file           : simple_main.c
 * @brief          : 简化双仪表盘主程序
 ******************************************************************************
 */

#include "main.h"
#include <stdio.h>

/* 外部函数声明 */
void SystemClock_Config(void);
void Error_Handler(void);
void MPU_Config(void);

/* LCD基础操作 - 直接内存映射 */
#define LCD_BASE    ((uint32_t)(0x60000000 | 0x0007FFFE))
#define LCD         ((LCD_TypeDef *) LCD_BASE)

typedef struct
{
    __IO uint16_t LCD_REG;
    __IO uint16_t LCD_RAM;
} LCD_TypeDef;

/* 颜色定义 */
#define WHITE   0xFFFF
#define BLACK   0x0000
#define RED     0xF800
#define GREEN   0x07E0
#define BLUE    0x001F

/* LCD基础函数 */
void lcd_write_reg(uint16_t regval)
{
    LCD->LCD_REG = regval;
}

void lcd_write_data(uint16_t data)
{
    LCD->LCD_RAM = data;
}

void lcd_clear_screen(uint16_t color)
{
    uint32_t i;
    
    lcd_write_reg(0x2A);    // Column Address Set
    lcd_write_data(0);
    lcd_write_data(0);
    lcd_write_data(1);
    lcd_write_data(0xDF);   // 479
    
    lcd_write_reg(0x2B);    // Page Address Set  
    lcd_write_data(0);
    lcd_write_data(0);
    lcd_write_data(1);
    lcd_write_data(0x3F);   // 319
    
    lcd_write_reg(0x2C);    // Memory Write
    
    for(i = 0; i < 480 * 320; i++) {
        lcd_write_data(color);
    }
}

void lcd_draw_point(uint16_t x, uint16_t y, uint16_t color)
{
    lcd_write_reg(0x2A);    // Column Address Set
    lcd_write_data(x >> 8);
    lcd_write_data(x & 0xFF);
    lcd_write_data(x >> 8);
    lcd_write_data(x & 0xFF);
    
    lcd_write_reg(0x2B);    // Page Address Set
    lcd_write_data(y >> 8);
    lcd_write_data(y & 0xFF);
    lcd_write_data(y >> 8);
    lcd_write_data(y & 0xFF);
    
    lcd_write_reg(0x2C);    // Memory Write
    lcd_write_data(color);
}

/* 简化的圆形绘制 */
void draw_simple_circle(uint16_t x0, uint16_t y0, uint16_t r, uint16_t color)
{
    int16_t x = r;
    int16_t y = 0;
    int16_t err = 0;
    
    while(x >= y) {
        lcd_draw_point(x0 + x, y0 + y, color);
        lcd_draw_point(x0 + y, y0 + x, color);
        lcd_draw_point(x0 - y, y0 + x, color);
        lcd_draw_point(x0 - x, y0 + y, color);
        lcd_draw_point(x0 - x, y0 - y, color);
        lcd_draw_point(x0 - y, y0 - x, color);
        lcd_draw_point(x0 + y, y0 - x, color);
        lcd_draw_point(x0 + x, y0 - y, color);
        
        if(err <= 0) {
            y += 1;
            err += 2*y + 1;
        }
        if(err > 0) {
            x -= 1;
            err -= 2*x + 1;
        }
    }
}

/* 简化的线条绘制 */
void draw_simple_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    int16_t dx = (x2 > x1) ? (x2 - x1) : (x1 - x2);
    int16_t dy = (y2 > y1) ? (y2 - y1) : (y1 - y2);
    int16_t sx = (x1 < x2) ? 1 : -1;
    int16_t sy = (y1 < y2) ? 1 : -1;
    int16_t err = dx - dy;
    
    while(1) {
        lcd_draw_point(x1, y1, color);
        
        if(x1 == x2 && y1 == y2) break;
        
        int16_t e2 = 2 * err;
        if(e2 > -dy) {
            err -= dy;
            x1 += sx;
        }
        if(e2 < dx) {
            err += dx;
            y1 += sy;
        }
    }
}

/* 简化的仪表盘绘制 */
void draw_gauge(uint16_t center_x, uint16_t center_y, uint16_t value)
{
    /* 绘制外圈 */
    draw_simple_circle(center_x, center_y, 50, WHITE);
    
    /* 绘制刻度线 */
    for(int i = 0; i < 12; i++) {
        float angle = -3.14159f * 0.75f + (i * 3.14159f * 1.5f / 11.0f);
        int16_t x1 = center_x + (int16_t)(45.0f * cosf(angle));
        int16_t y1 = center_y + (int16_t)(45.0f * sinf(angle));
        int16_t x2 = center_x + (int16_t)(40.0f * cosf(angle));
        int16_t y2 = center_y + (int16_t)(40.0f * sinf(angle));
        draw_simple_line(x1, y1, x2, y2, WHITE);
    }
    
    /* 绘制指针 */
    if(value > 100) value = 100;
    float pointer_angle = -3.14159f * 0.75f + (value * 3.14159f * 1.5f / 100.0f);
    int16_t px = center_x + (int16_t)(35.0f * cosf(pointer_angle));
    int16_t py = center_y + (int16_t)(35.0f * sinf(pointer_angle));
    draw_simple_line(center_x, center_y, px, py, RED);
    
    /* 绘制中心点 */
    for(int i = 0; i < 3; i++) {
        draw_simple_circle(center_x, center_y, i, BLUE);
    }
}

/* 清除指针区域 */
void clear_gauge_center(uint16_t center_x, uint16_t center_y)
{
    for(int y = -40; y <= 40; y++) {
        for(int x = -40; x <= 40; x++) {
            if(x*x + y*y <= 40*40) {
                lcd_draw_point(center_x + x, center_y + y, BLACK);
            }
        }
    }
}

/**
 * @brief  主函数
 * @retval int
 */
int main(void)
{
    int32_t timeout;
    
    /* MPU Configuration */
    MPU_Config();
    
    /* Wait until CPU2 boots */
    timeout = 0xFFFF;
    while((__HAL_RCC_GET_FLAG(RCC_FLAG_D2CKRDY) != RESET) && (timeout-- > 0));
    if(timeout < 0) {
        Error_Handler();
    }
    
    /* MCU Configuration */
    HAL_Init();
    SystemClock_Config();
    
    /* Release CPU2 */
    __HAL_RCC_HSEM_CLK_ENABLE();
    HAL_HSEM_FastTake(0);
    HAL_HSEM_Release(0, 0);
    
    timeout = 0xFFFF;
    while((__HAL_RCC_GET_FLAG(RCC_FLAG_D2CKRDY) == RESET) && (timeout-- > 0));
    if(timeout < 0) {
        Error_Handler();
    }
    
    printf("STM32H745 双仪表盘演示启动\r\n");
    
    /* 清屏 */
    lcd_clear_screen(BLACK);
    
    /* 初始化双仪表盘 */
    draw_gauge(120, 160, 0);  // 左侧仪表盘
    draw_gauge(360, 160, 0);  // 右侧仪表盘
    
    printf("双仪表盘初始化完成\r\n");
    
    /* 演示循环 */
    uint16_t left_value = 0, right_value = 0;
    uint8_t left_dir = 0, right_dir = 0;
    
    while(1) {
        /* 更新左侧数值 */
        if(left_dir == 0) {
            if(left_value < 100) left_value += 5;
            else left_dir = 1;
        } else {
            if(left_value > 0) left_value -= 5;
            else left_dir = 0;
        }
        
        /* 更新右侧数值 */
        if(right_dir == 0) {
            if(right_value < 100) right_value += 3;
            else right_dir = 1;
        } else {
            if(right_value > 0) right_value -= 3;
            else right_dir = 0;
        }
        
        /* 清除并重绘仪表盘 */
        clear_gauge_center(120, 160);
        draw_gauge(120, 160, left_value);
        
        clear_gauge_center(360, 160);
        draw_gauge(360, 160, right_value);
        
        printf("Left: %d%%, Right: %d%%\r\n", left_value, right_value);
        
        HAL_Delay(200);
    }
}
