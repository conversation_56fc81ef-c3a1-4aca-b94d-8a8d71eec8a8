/**
 ******************************************************************************
 * @file           : gauge_test.c
 * @brief          : 双仪表盘测试程序
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 仪表盘功能测试和演示程序
 ******************************************************************************
 */

#include "gauge_test.h"
#include "gauge.h"
#include <stdio.h>

/**
 * @brief       简单仪表盘测试
 * @param       无
 * @retval      无
 */
void gauge_simple_test(void)
{
    printf("=== 简单仪表盘测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 测试固定数值 */
    gauge_draw_both(30, 70);
    HAL_Delay(2000);
    
    gauge_draw_both(60, 40);
    HAL_Delay(2000);
    
    gauge_draw_both(90, 10);
    HAL_Delay(2000);
    
    printf("简单仪表盘测试完成\r\n");
}

/**
 * @brief       仪表盘动画测试
 * @param       无
 * @retval      无
 */
void gauge_animation_test(void)
{
    printf("=== 仪表盘动画测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 从0到100的动画 */
    for(uint16_t i = 0; i <= 100; i += 5) {
        gauge_draw_both(i, 100 - i);
        HAL_Delay(200);
    }
    
    /* 从100到0的动画 */
    for(uint16_t i = 100; i > 0; i -= 5) {
        gauge_draw_both(i, 100 - i);
        HAL_Delay(200);
    }
    
    printf("仪表盘动画测试完成\r\n");
}

/**
 * @brief       仪表盘随机测试
 * @param       无
 * @retval      无
 */
void gauge_random_test(void)
{
    printf("=== 仪表盘随机测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 随机数值测试 */
    uint32_t seed = HAL_GetTick();
    for(uint16_t i = 0; i < 50; i++) {
        seed = seed * 1103515245 + 12345; // 简单的线性同余生成器
        uint16_t left_val = (seed >> 16) % 101;
        
        seed = seed * 1103515245 + 12345;
        uint16_t right_val = (seed >> 16) % 101;
        
        gauge_draw_both(left_val, right_val);
        printf("随机值: Left=%d, Right=%d\r\n", left_val, right_val);
        HAL_Delay(500);
    }
    
    printf("仪表盘随机测试完成\r\n");
}

/**
 * @brief       仪表盘性能测试
 * @param       无
 * @retval      无
 */
void gauge_performance_test(void)
{
    printf("=== 仪表盘性能测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    uint32_t start_time = HAL_GetTick();
    
    /* 快速更新测试 */
    for(uint16_t i = 0; i < 100; i++) {
        gauge_draw_both(i, 100 - i);
    }
    
    uint32_t end_time = HAL_GetTick();
    uint32_t elapsed = end_time - start_time;
    
    printf("100次更新耗时: %lu ms\r\n", elapsed);
    printf("平均每次更新: %lu ms\r\n", elapsed / 100);
    printf("仪表盘性能测试完成\r\n");
}

/**
 * @brief       仪表盘综合测试
 * @param       无
 * @retval      无
 */
void gauge_comprehensive_test(void)
{
    printf("=== 仪表盘综合测试开始 ===\r\n");
    
    /* 1. 简单测试 */
    gauge_simple_test();
    HAL_Delay(1000);
    
    /* 2. 动画测试 */
    gauge_animation_test();
    HAL_Delay(1000);
    
    /* 3. 随机测试 */
    gauge_random_test();
    HAL_Delay(1000);
    
    /* 4. 性能测试 */
    gauge_performance_test();
    
    printf("=== 仪表盘综合测试完成 ===\r\n");
}

/**
 * @brief       仪表盘单独更新测试
 * @param       无
 * @retval      无
 */
void gauge_individual_test(void)
{
    printf("=== 仪表盘单独更新测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 只更新左侧仪表盘 */
    printf("只更新左侧仪表盘...\r\n");
    for(uint16_t i = 0; i <= 100; i += 10) {
        gauge_update_value(GAUGE_LEFT, i);
        HAL_Delay(300);
    }
    
    HAL_Delay(1000);
    
    /* 只更新右侧仪表盘 */
    printf("只更新右侧仪表盘...\r\n");
    for(uint16_t i = 0; i <= 100; i += 10) {
        gauge_update_value(GAUGE_RIGHT, i);
        HAL_Delay(300);
    }
    
    printf("仪表盘单独更新测试完成\r\n");
}

/**
 * @brief       仪表盘边界值测试
 * @param       无
 * @retval      无
 */
void gauge_boundary_test(void)
{
    printf("=== 仪表盘边界值测试 ===\r\n");
    
    /* 初始化仪表盘 */
    gauge_init();
    
    /* 测试边界值 */
    printf("测试最小值 0...\r\n");
    gauge_draw_both(0, 0);
    HAL_Delay(2000);
    
    printf("测试最大值 100...\r\n");
    gauge_draw_both(100, 100);
    HAL_Delay(2000);
    
    printf("测试超出范围值 150...\r\n");
    gauge_draw_both(150, 150); // 应该被限制为100
    HAL_Delay(2000);
    
    printf("测试中间值 50...\r\n");
    gauge_draw_both(50, 50);
    HAL_Delay(2000);
    
    printf("仪表盘边界值测试完成\r\n");
}

/**
 * @brief       仪表盘完整演示
 * @param       无
 * @retval      无
 */
void gauge_full_demo(void)
{
    printf("=== 双仪表盘完整演示开始 ===\r\n");
    
    /* 运行所有测试 */
    gauge_boundary_test();
    HAL_Delay(1000);
    
    gauge_individual_test();
    HAL_Delay(1000);
    
    gauge_comprehensive_test();
    
    printf("=== 双仪表盘完整演示结束 ===\r\n");
}
