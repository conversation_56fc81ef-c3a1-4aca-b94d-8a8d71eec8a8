# STM32H745 LCD驱动项目

## 项目概述
本项目为STM32H745双核微控制器的LCD驱动实现，支持多种LCD控制器，包括ILI9341、ST7789、NT35310、ST7796、NT35510、ILI9806和SSD1963等。

## 硬件配置

### 引脚配置
| 功能 | GPIO端口 | GPIO引脚 | FMC功能 | 说明 |
|------|----------|----------|---------|------|
| LCD_CS | GPIOD | PIN_7 | FMC_NE1 | 片选信号 |
| LCD_RS | GPIOE | PIN_3 | FMC_A19 | 寄存器选择 |
| LCD_WR | GPIOD | PIN_5 | FMC_NWE | 写使能 |
| LCD_RD | GPIOD | PIN_4 | FMC_NOE | 读使能 |
| LCD_BL | GPIOB | PIN_5 | GPIO | 背光控制 |

### FMC配置
- 使用FMC_NE1作为片选信号
- 使用FMC_A19作为寄存器选择信号
- 16位数据总线
- 支持异步读写时序

## 文件结构

### 核心文件
- `CM7/Core/Inc/lcd.h` - LCD驱动头文件，包含所有函数声明和宏定义
- `CM7/Core/Src/lcd.c` - LCD驱动源文件，包含基础驱动函数实现
- `CM7/Core/Inc/main.h` - 主头文件，包含系统级声明

### 主要功能模块

#### 1. 基础驱动函数
```c
void lcd_wr_data(volatile uint16_t data);           // 写数据
void lcd_wr_regno(volatile uint16_t regno);         // 写寄存器地址
void lcd_write_reg(uint16_t regno, uint16_t data);  // 写寄存器
uint32_t lcd_read_point(uint16_t x, uint16_t y);    // 读点颜色
void lcd_set_cursor(uint16_t x, uint16_t y);        // 设置光标
void lcd_set_window(uint16_t sx, uint16_t sy, uint16_t width, uint16_t height); // 设置窗口
```

#### 2. 初始化函数
```c
void lcd_init(void);                    // LCD初始化
void HAL_SRAM_MspInit(SRAM_HandleTypeDef *hsram); // FMC底层初始化
```

#### 3. 显示控制函数
```c
void lcd_display_dir(uint8_t dir);      // 设置显示方向 (需要用户实现)
void lcd_scan_dir(uint8_t dir);         // 设置扫描方向 (需要用户实现)
void lcd_clear(uint32_t color);         // 清屏 (需要用户实现)
```

## 使用方法

### 1. 初始化LCD
```c
#include "lcd.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // LCD初始化
    lcd_init();
    
    // 其他代码...
}
```

### 2. 基本显示操作
```c
// 设置显示方向 (0=竖屏, 1=横屏)
lcd_display_dir(0);

// 清屏为白色
lcd_clear(WHITE);

// 设置画笔颜色
g_point_color = RED;

// 画点
lcd_draw_point(100, 100);
```

### 3. 颜色定义
项目预定义了常用颜色：
```c
#define WHITE           0xFFFF      // 白色
#define BLACK           0x0000      // 黑色
#define RED             0xF800      // 红色
#define GREEN           0x07E0      // 绿色
#define BLUE            0x001F      // 蓝色
#define YELLOW          0XFFE0      // 黄色
#define CYAN            0X07FF      // 青色
#define MAGENTA         0XF81F      // 品红色
```

## 支持的LCD控制器

### 自动识别的LCD类型
- **ILI9341** (ID: 0x9341) - 240x320分辨率
- **ST7789** (ID: 0x7789) - 240x320分辨率  
- **NT35310** (ID: 0x5310) - 320x480分辨率
- **ST7796** (ID: 0x7796) - 320x480分辨率
- **NT35510** (ID: 0x5510) - 480x800分辨率
- **ILI9806** (ID: 0x9806) - 480x800分辨率
- **SSD1963** (ID: 0x1963) - 480x800分辨率

### LCD初始化函数 (需要用户实现)
每种LCD控制器都需要对应的初始化函数：
```c
void lcd_ex_ili9341_reginit(void);  // ILI9341初始化
void lcd_ex_st7789_reginit(void);   // ST7789初始化
void lcd_ex_nt35310_reginit(void);  // NT35310初始化
void lcd_ex_st7796_reginit(void);   // ST7796初始化
void lcd_ex_nt35510_reginit(void);  // NT35510初始化
void lcd_ex_ili9806_reginit(void);  // ILI9806初始化
void lcd_ex_ssd1963_reginit(void);  // SSD1963初始化
```

## 待实现功能

### 必须实现的函数
以下函数在头文件中已声明，但需要用户根据具体需求实现：

1. **显示控制函数**
   - `void lcd_display_dir(uint8_t dir)` - 设置显示方向
   - `void lcd_scan_dir(uint8_t dir)` - 设置扫描方向
   - `void lcd_clear(uint32_t color)` - 清屏函数

2. **绘图函数**
   - `void lcd_draw_point(uint16_t x, uint16_t y)` - 画点
   - `void lcd_draw_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2)` - 画线
   - `void lcd_draw_rectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2)` - 画矩形
   - `void lcd_fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color)` - 填充区域

3. **字符显示函数**
   - `void lcd_show_char(uint16_t x, uint16_t y, uint8_t chr, uint8_t size, uint8_t mode)` - 显示字符
   - `void lcd_show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t size, char *p)` - 显示字符串

4. **各LCD控制器初始化函数** (如上所述)

5. **工具函数**
   - `void delay_ms(uint32_t ms)` - 毫秒延时函数

## 注意事项

1. **时钟配置**: 确保FMC时钟已正确配置，建议FMC时钟频率为220MHz
2. **引脚配置**: 所有LCD相关引脚必须正确配置为FMC复用功能
3. **时序优化**: 不同LCD控制器有不同的时序要求，已在初始化中自动优化
4. **内存映射**: LCD寄存器和数据通过FMC内存映射访问，基地址自动计算
5. **背光控制**: 背光通过GPIO控制，可使用LCD_BL(1)开启，LCD_BL(0)关闭

## 编译要求

- STM32CubeIDE或Keil MDK
- STM32H7 HAL库
- 支持C99标准的编译器

## 版本信息

- 版本: 1.0.0
- 创建日期: 2025-01-04
- 适用芯片: STM32H745XIH6
- 开发环境: STM32CubeIDE
