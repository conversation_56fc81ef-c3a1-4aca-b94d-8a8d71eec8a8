# STM32H745 LCD驱动项目

## 项目概述
本项目为STM32H745双核微控制器的LCD驱动实现，支持多种LCD控制器，包括ILI9341、ST7789、NT35310、ST7796、NT35510、ILI9806和SSD1963等。

## 硬件配置

### 引脚配置
| 功能 | GPIO端口 | GPIO引脚 | FMC功能 | 说明 |
|------|----------|----------|---------|------|
| LCD_CS | GPIOD | PIN_7 | FMC_NE1 | 片选信号 |
| LCD_RS | GPIOE | PIN_3 | FMC_A19 | 寄存器选择 |
| LCD_WR | GPIOD | PIN_5 | FMC_NWE | 写使能 |
| LCD_RD | GPIOD | PIN_4 | FMC_NOE | 读使能 |
| LCD_BL | GPIOB | PIN_5 | GPIO | 背光控制 |

### FMC配置
- 使用FMC_NE1作为片选信号
- 使用FMC_A19作为寄存器选择信号
- 16位数据总线
- 支持异步读写时序

## 文件结构

### 核心文件
- `CM7/Core/Inc/lcd.h` - LCD驱动头文件，包含所有函数声明和宏定义
- `CM7/Core/Src/lcd.c` - LCD驱动源文件，包含基础驱动函数实现 (1281行)
- `CM7/Core/Src/lcd_test.c` - LCD测试示例代码，展示驱动使用方法 (586行)
- `CM7/Core/Src/font.c` - 字体数据文件，包含ASCII字符点阵数据 (新增)
- `CM7/Core/Inc/font.h` - 字体头文件，字体数组声明 (新增)
- `CM7/Core/Inc/main.h` - 主头文件，包含系统级声明

### 主要功能模块

#### 1. 基础驱动函数
```c
void lcd_wr_data(volatile uint16_t data);           // 写数据
void lcd_wr_regno(volatile uint16_t regno);         // 写寄存器地址
void lcd_write_reg(uint16_t regno, uint16_t data);  // 写寄存器
uint32_t lcd_read_point(uint16_t x, uint16_t y);    // 读点颜色
void lcd_set_cursor(uint16_t x, uint16_t y);        // 设置光标
void lcd_set_window(uint16_t sx, uint16_t sy, uint16_t width, uint16_t height); // 设置窗口
```

#### 2. 初始化函数
```c
void lcd_init(void);                    // LCD初始化
void HAL_SRAM_MspInit(SRAM_HandleTypeDef *hsram); // FMC底层初始化
void lcd_scan_dir(uint8_t dir);         // 设置扫描方向
void delay_ms(uint32_t ms);             // 延时函数
```

#### 3. 显示控制函数
```c
void lcd_display_on(void);              // 开启显示 (已实现)
void lcd_display_off(void);             // 关闭显示 (已实现)
void lcd_display_dir(uint8_t dir);      // 设置显示方向 (已实现)
void lcd_scan_dir(uint8_t dir);         // 设置扫描方向 (已实现)
void lcd_set_cursor(uint16_t x, uint16_t y); // 设置光标 (已实现)
void lcd_set_window(uint16_t sx, uint16_t sy, uint16_t width, uint16_t height); // 设置窗口 (已实现)
void lcd_clear(uint32_t color);         // 清屏 (已实现)
```

#### 4. 绘图函数
```c
void lcd_draw_point(uint16_t x, uint16_t y, uint32_t color); // 画点 (已实现)
void lcd_clear(uint16_t color);         // 清屏 (已实现)
void lcd_fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint32_t color); // 填充区域 (已实现)
void lcd_color_fill(uint16_t sx, uint16_t sy, uint16_t ex, uint16_t ey, uint16_t *color); // 填充指定颜色 (已实现)
void lcd_draw_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color); // 画线 (已实现)
void lcd_draw_hline(uint16_t x, uint16_t y, uint16_t len, uint16_t color); // 画水平线 (已实现)
void lcd_draw_rectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color); // 画矩形 (已实现)
void lcd_draw_circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color); // 画圆 (已实现)
void lcd_fill_circle(uint16_t x, uint16_t y, uint16_t r, uint16_t color); // 填充圆 (已实现)
void lcd_ssd_backlight_set(uint8_t pwm); // SSD1963背光控制 (已实现)
```

#### 5. 字符显示函数
```c
void lcd_show_char(uint16_t x, uint16_t y, char chr, uint8_t size, uint8_t mode, uint16_t color); // 显示字符 (已实现)
void lcd_show_num(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint16_t color); // 显示数字 (已实现)
void lcd_show_xnum(uint16_t x, uint16_t y, uint32_t num, uint8_t len, uint8_t size, uint8_t mode, uint16_t color); // 扩展显示数字 (已实现)
void lcd_show_string(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t size, char *p, uint16_t color); // 显示字符串 (已实现)
```

## 使用方法

### 1. 初始化LCD
```c
#include "lcd.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    
    // LCD初始化
    lcd_init();
    
    // 其他代码...
}
```

### 2. 基本显示操作
```c
// 开启LCD显示
lcd_display_on();

// 设置显示方向 (0=竖屏, 1=横屏)
lcd_display_dir(0);

// 设置扫描方向
lcd_scan_dir(L2R_U2D);  // 从左到右，从上到下

// 设置光标位置
lcd_set_cursor(100, 100);

// 清屏为白色
lcd_clear(WHITE);

// 设置画笔颜色
g_point_color = RED;

// 设置窗口
lcd_set_window(0, 0, 240, 320);  // 全屏窗口

// 画点
lcd_draw_point(100, 100, RED);  // 在(100,100)画红色点

// 绘图功能
lcd_draw_line(0, 0, 100, 100, GREEN);           // 画绿色线
lcd_draw_rectangle(50, 50, 150, 100, BLUE);     // 画蓝色矩形
lcd_draw_circle(120, 160, 30, YELLOW);          // 画黄色圆
lcd_draw_hline(10, 200, 100, MAGENTA);          // 画品红色水平线

// 填充功能
lcd_fill(10, 10, 80, 80, CYAN);                 // 填充青色矩形
lcd_fill_circle(120, 160, 30, YELLOW);          // 填充黄色圆

// 字符显示功能
lcd_show_char(10, 10, 'A', 16, 0, RED);         // 显示字符A
lcd_show_num(10, 40, 12345, 5, 16, GREEN);      // 显示数字12345
lcd_show_string(10, 70, 200, 16, 16, "Hello!", BLUE); // 显示字符串

// SSD1963背光控制 (仅适用于SSD1963)
if(lcddev.id == 0x1963) {
    lcd_ssd_backlight_set(80);  // 设置背光亮度80%
}

// 关闭LCD显示 (可选)
// lcd_display_off();
```

### 2. 测试示例使用
```c
#include "lcd_test.c"  // 包含测试示例

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();

    // 运行LCD测试
    lcd_test_example();         // 基础功能测试
    lcd_show_info();            // 显示LCD信息
    lcd_scan_dir_test();        // 扫描方向测试
    lcd_basic_test();           // 基础功能测试
    lcd_display_switch_test();  // 显示开关测试
    lcd_cursor_test();          // 光标设置测试
    lcd_display_dir_test();     // 显示方向测试
    lcd_draw_point_test();      // 画点功能测试
    lcd_backlight_test();       // 背光测试 (仅SSD1963)
    lcd_window_test();          // 窗口设置测试

    while(1)
    {
        // 主循环
    }
}
```

### 3. 扫描方向设置
```c
// 设置LCD扫描方向 (0-7对应8个方向)
lcd_scan_dir(L2R_U2D);  // 从左到右,从上到下 (默认)

// 扫描方向定义
#define L2R_U2D         0           // 从左到右,从上到下
#define L2R_D2U         1           // 从左到右,从下到上
#define R2L_U2D         2           // 从右到左,从上到下
#define R2L_D2U         3           // 从右到左,从下到上
#define U2D_L2R         4           // 从上到下,从左到右
#define U2D_R2L         5           // 从上到下,从右到左
#define D2U_L2R         6           // 从下到上,从左到右
#define D2U_R2L         7           // 从下到上,从右到左
```

### 4. 颜色定义
项目预定义了常用颜色：
```c
#define WHITE           0xFFFF      // 白色
#define BLACK           0x0000      // 黑色
#define RED             0xF800      // 红色
#define GREEN           0x07E0      // 绿色
#define BLUE            0x001F      // 蓝色
#define YELLOW          0XFFE0      // 黄色
#define CYAN            0X07FF      // 青色
#define MAGENTA         0XF81F      // 品红色
```

## 支持的LCD控制器

### 自动识别的LCD类型
- **ILI9341** (ID: 0x9341) - 240x320分辨率
- **ST7789** (ID: 0x7789) - 240x320分辨率  
- **NT35310** (ID: 0x5310) - 320x480分辨率
- **ST7796** (ID: 0x7796) - 320x480分辨率
- **NT35510** (ID: 0x5510) - 480x800分辨率
- **ILI9806** (ID: 0x9806) - 480x800分辨率
- **SSD1963** (ID: 0x1963) - 480x800分辨率

### LCD初始化函数 (需要用户实现)
每种LCD控制器都需要对应的初始化函数：
```c
void lcd_ex_ili9341_reginit(void);  // ILI9341初始化
void lcd_ex_st7789_reginit(void);   // ST7789初始化
void lcd_ex_nt35310_reginit(void);  // NT35310初始化
void lcd_ex_st7796_reginit(void);   // ST7796初始化
void lcd_ex_nt35510_reginit(void);  // NT35510初始化
void lcd_ex_ili9806_reginit(void);  // ILI9806初始化
void lcd_ex_ssd1963_reginit(void);  // SSD1963初始化
```

## 待实现功能

### 必须实现的函数
以下函数在头文件中已声明，但需要用户根据具体需求实现：

1. **各LCD控制器初始化函数** (如上所述)

## 注意事项

1. **时钟配置**: 确保FMC时钟已正确配置，建议FMC时钟频率为220MHz
2. **引脚配置**: 所有LCD相关引脚必须正确配置为FMC复用功能
3. **时序优化**: 不同LCD控制器有不同的时序要求，已在初始化中自动优化
4. **内存映射**: LCD寄存器和数据通过FMC内存映射访问，基地址自动计算
5. **背光控制**: 背光通过GPIO控制，可使用LCD_BL(1)开启，LCD_BL(0)关闭

## 编译和下载

### 编译要求
- Keil MDK-ARM 5.29或更高版本
- IAR EWARM 8.50或更高版本
- STM32CubeIDE 1.8.0或更高版本
- STM32H7 HAL库
- 支持C99标准的编译器

### 编译步骤

#### 使用Keil MDK-ARM
1. 打开 `MDK-ARM/LCD.uvprojx` 项目文件
2. 选择目标配置（CM7或CM4）
3. 点击编译按钮或按F7编译
4. 编译成功后生成 `.hex` 或 `.bin` 文件

#### 使用IAR EWARM
1. 打开 `EWARM/Project.eww` 工作空间文件
2. 选择目标配置（CM7或CM4）
3. 点击编译按钮或按F7编译
4. 编译成功后生成 `.hex` 或 `.bin` 文件

#### 使用STM32CubeIDE
1. 导入现有项目到工作空间
2. 选择项目根目录
3. 右键项目 -> Build Project
4. 编译成功后在Debug文件夹生成 `.elf` 文件

### 下载到开发板

#### 使用ST-Link
1. 连接ST-Link调试器到开发板
2. 在IDE中点击下载按钮
3. 或使用STM32CubeProgrammer下载hex文件

#### 使用J-Link
1. 连接J-Link调试器到开发板
2. 在Keil或IAR中配置J-Link调试器
3. 点击下载按钮

### 测试函数调用
在main函数中调用以下测试函数：
```c
#include "lcd_test.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();

    // 串口初始化（用于调试输出）
    // MX_USART1_UART_Init(); // 根据实际配置

    // LCD综合测试
    lcd_comprehensive_test();

    while(1)
    {
        // 主循环
    }
}
```

## 版本信息

- 版本: 1.0.0
- 创建日期: 2025-01-04
- 适用芯片: STM32H745XIH6
- 开发环境: STM32CubeIDE
