


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       *************************
    2 00000000         ;* File Name          : startup_stm32h745xx.s
    3 00000000         ;* <AUTHOR> Application Team
    4 00000000         ;* Description        : STM32H7xx devices vector table f
                       or MDK-ARM toolchain. 
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the Cortex-M process
                       or is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   14 00000000         ;*******************************************************
                       ***********************
   15 00000000         ;* @attention
   16 00000000         ;*
   17 00000000         ;* Copyright (c) 2019 STMicroelectronics.
   18 00000000         ;* All rights reserved.
   19 00000000         ;*
   20 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   21 00000000         ;* in the root directory of this software component.
   22 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   23 00000000         ;*
   24 00000000         ;*******************************************************
                       ************************
   25 00000000         
   26 00000000         ; Amount of memory (in bytes) allocated for Stack
   27 00000000         ; Tailor this value to your application needs
   28 00000000         ; <h> Stack Configuration
   29 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   30 00000000         ; </h>
   31 00000000         
   32 00000000 00000400 
                       Stack_Size
                               EQU              0x400
   33 00000000         
   34 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   35 00000000         Stack_Mem
                               SPACE            Stack_Size
   36 00000400         __initial_sp
   37 00000400         
   38 00000400         
   39 00000400         ; <h> Heap Configuration
   40 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000400         ; </h>
   42 00000400         
   43 00000400 00000200 
                       Heap_Size



ARM Macro Assembler    Page 2 


                               EQU              0x200
   44 00000400         
   45 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   46 00000000         __heap_base
   47 00000000         Heap_Mem
                               SPACE            Heap_Size
   48 00000200         __heap_limit
   49 00000200         
   50 00000200                 PRESERVE8
   51 00000200                 THUMB
   52 00000200         
   53 00000200         
   54 00000200         ; Vector Table Mapped to Address 0 at Reset
   55 00000200                 AREA             RESET, DATA, READONLY
   56 00000000                 EXPORT           __Vectors
   57 00000000                 EXPORT           __Vectors_End
   58 00000000                 EXPORT           __Vectors_Size
   59 00000000         
   60 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   61 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   62 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   63 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   64 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   65 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   66 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   67 0000001C 00000000        DCD              0           ; Reserved
   68 00000020 00000000        DCD              0           ; Reserved
   69 00000024 00000000        DCD              0           ; Reserved
   70 00000028 00000000        DCD              0           ; Reserved
   71 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   72 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   73 00000034 00000000        DCD              0           ; Reserved
   74 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   75 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   76 00000040         
   77 00000040         ; External Interrupts
   78 00000040 00000000        DCD              WWDG_IRQHandler ; Window WatchD
                                                            og interrupt ( wwdg
                                                            1_it, wwdg2_it)    
                                                                               
                                                                              
   79 00000044 00000000        DCD              PVD_AVD_IRQHandler ; PVD/AVD th
                                                            rough EXTI Line det
                                                            ection             
                                                                       
   80 00000048 00000000        DCD              TAMP_STAMP_IRQHandler ; Tamper 
                                                            and TimeStamps thro



ARM Macro Assembler    Page 3 


                                                            ugh the EXTI line  
                                                                      
   81 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line               
                                                                    
   82 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH       
                                                                               
                                                                             
   83 00000054 00000000        DCD              RCC_IRQHandler ; RCC           
                                                                               
                                                                           
   84 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0  
                                                                               
                                                                               
                                                                 
   85 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1  
                                                                               
                                                                               
                                                                 
   86 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2  
                                                                               
                                                                               
                                                                 
   87 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3  
                                                                               
                                                                               
                                                                 
   88 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4 
   89 0000006C 00000000        DCD              DMA1_Stream0_IRQHandler 
                                                            ; DMA1 Stream 0
   90 00000070 00000000        DCD              DMA1_Stream1_IRQHandler ; DMA1 
                                                            Stream 1           
                                                                               
                                                                 
   91 00000074 00000000        DCD              DMA1_Stream2_IRQHandler ; DMA1 
                                                            Stream 2           
                                                                               
                                                                 
   92 00000078 00000000        DCD              DMA1_Stream3_IRQHandler ; DMA1 
                                                            Stream 3           
                                                                               
                                                                 
   93 0000007C 00000000        DCD              DMA1_Stream4_IRQHandler ; DMA1 
                                                            Stream 4           
                                                                               
                                                                 
   94 00000080 00000000        DCD              DMA1_Stream5_IRQHandler ; DMA1 
                                                            Stream 5           
                                                                               
                                                                 
   95 00000084 00000000        DCD              DMA1_Stream6_IRQHandler 
                                                            ; DMA1 Stream 6  
   96 00000088 00000000        DCD              ADC_IRQHandler ; ADC1, ADC2    
                                                                               
                                                                  
   97 0000008C 00000000        DCD              FDCAN1_IT0_IRQHandler ; FDCAN1 
                                                            interrupt line 0   
                                                                               



ARM Macro Assembler    Page 4 


                                                              
   98 00000090 00000000        DCD              FDCAN2_IT0_IRQHandler ; FDCAN2 
                                                            interrupt line 0   
                                                                               
                                                                     
   99 00000094 00000000        DCD              FDCAN1_IT1_IRQHandler ; FDCAN1 
                                                            interrupt line 1   
                                                                               
                                                              
  100 00000098 00000000        DCD              FDCAN2_IT1_IRQHandler ; FDCAN2 
                                                            interrupt line 1   
                                                                               
                                                                               
                                                                  
  101 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s          
                                                                               
                                                                   
  102 000000A0 00000000        DCD              TIM1_BRK_IRQHandler ; TIM1 Brea
                                                            k interrupt        
                                                                       
  103 000000A4 00000000        DCD              TIM1_UP_IRQHandler ; TIM1 Updat
                                                            e Interrupt        
                                                                     
  104 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion Interrupt 
  105 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare         
                                                                               
                                                                   
  106 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2         
                                                                               
                                                                            
  107 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3         
                                                                               
                                                                            
  108 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4         
                                                                               
                                                                            
  109 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                                               
                                                                               
                                                                   
  110 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                                               
                                                                               
                                                                   
  111 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                                               
                                                                               
                                                                   
  112 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                                               
                                                                               
                                                                     
  113 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1         
                                                                               
                                                                            



ARM Macro Assembler    Page 5 


  114 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2         
                                                                               
                                                                            
  115 000000D4 00000000        DCD              USART1_IRQHandler ; USART1     
                                                                               
                                                                              
  116 000000D8 00000000        DCD              USART2_IRQHandler ; USART2     
                                                                               
                                                                              
  117 000000DC 00000000        DCD              USART3_IRQHandler ; USART3     
                                                                               
                                                                              
  118 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]  
  119 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line
  120 000000E8 00000000        DCD              0           ; Reserved         
                                                                               
                                                                          
  121 000000EC 00000000        DCD              TIM8_BRK_TIM12_IRQHandler ; TIM
                                                            8 Break Interrupt a
                                                            nd TIM12 global int
                                                            errupt             
                                                                
  122 000000F0 00000000        DCD              TIM8_UP_TIM13_IRQHandler ; TIM8
                                                             Update Interrupt a
                                                            nd TIM13 global int
                                                            errupt
  123 000000F4 00000000        DCD              TIM8_TRG_COM_TIM14_IRQHandler ;
                                                             TIM8 Trigger and C
                                                            ommutation Interrup
                                                            t and TIM14 global 
                                                            interrupt
  124 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare Interrup
                                                            t
  125 000000FC 00000000        DCD              DMA1_Stream7_IRQHandler ; DMA1 
                                                            Stream7            
                                                                               
                                                                        
  126 00000100 00000000        DCD              FMC_IRQHandler ; FMC           
                                                                              
  127 00000104 00000000        DCD              SDMMC1_IRQHandler ; SDMMC1     
                                                                               
                                                                
  128 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5         
                                                                               
                                                            
  129 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3         
                                                                               
                                                            
  130 00000110 00000000        DCD              UART4_IRQHandler ; UART4       
                                                                               
                                                             
  131 00000114 00000000        DCD              UART5_IRQHandler ; UART5       
                                                                               
                                                             
  132 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 



ARM Macro Assembler    Page 6 


                                                            DAC1&2 underrun err
                                                            ors           
  133 0000011C 00000000        DCD              TIM7_IRQHandler 
                                                            ; TIM7           
  134 00000120 00000000        DCD              DMA2_Stream0_IRQHandler ; DMA2 
                                                            Stream 0           
                                                                    
  135 00000124 00000000        DCD              DMA2_Stream1_IRQHandler ; DMA2 
                                                            Stream 1           
                                                                    
  136 00000128 00000000        DCD              DMA2_Stream2_IRQHandler ; DMA2 
                                                            Stream 2           
                                                                    
  137 0000012C 00000000        DCD              DMA2_Stream3_IRQHandler ; DMA2 
                                                            Stream 3           
                                                                    
  138 00000130 00000000        DCD              DMA2_Stream4_IRQHandler ; DMA2 
                                                            Stream 4           
                                                                    
  139 00000134 00000000        DCD              ETH_IRQHandler ; Ethernet      
                                                                              
  140 00000138 00000000        DCD              ETH_WKUP_IRQHandler ; Ethernet 
                                                            Wakeup through EXTI
                                                             line              
                                                            
  141 0000013C 00000000        DCD              FDCAN_CAL_IRQHandler ; FDCAN ca
                                                            libration unit inte
                                                            rrupt              
                                                                      
  142 00000140 00000000        DCD              CM7_SEV_IRQHandler ; CM7 Send e
                                                            vent interrupt for 
                                                            CM4                
                                                                          
  143 00000144 00000000        DCD              CM4_SEV_IRQHandler ; CM4 Send e
                                                            vent interrupt for 
                                                            CM7 
  144 00000148 00000000        DCD              0           ; Reserved 
  145 0000014C 00000000        DCD              0           ; Reserved         
                                                                         
  146 00000150 00000000        DCD              DMA2_Stream5_IRQHandler ; DMA2 
                                                            Stream 5           
                                                                    
  147 00000154 00000000        DCD              DMA2_Stream6_IRQHandler ; DMA2 
                                                            Stream 6           
                                                                    
  148 00000158 00000000        DCD              DMA2_Stream7_IRQHandler ; DMA2 
                                                            Stream 7           
                                                                    
  149 0000015C 00000000        DCD              USART6_IRQHandler ; USART6     
                                                                               
                                                               
  150 00000160 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 event
                                                                               
                                                                      
  151 00000164 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 error
                                                                               
                                                                      
  152 00000168 00000000        DCD              OTG_HS_EP1_OUT_IRQHandler ; USB
                                                             OTG HS End Point 1



ARM Macro Assembler    Page 7 


                                                             Out               
                                                                   
  153 0000016C 00000000        DCD              OTG_HS_EP1_IN_IRQHandler ; USB 
                                                            OTG HS End Point 1 
                                                            In                 
                                                                  
  154 00000170 00000000        DCD              OTG_HS_WKUP_IRQHandler ; USB OT
                                                            G HS Wakeup through
                                                             EXTI              
                                                                       
  155 00000174 00000000        DCD              OTG_HS_IRQHandler ; USB OTG HS 
                                                                               
                                                            
  156 00000178 00000000        DCD              DCMI_IRQHandler ; DCMI         
                                                                               
                                                            
  157 0000017C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                     
  158 00000180 00000000        DCD              RNG_IRQHandler ; Rng
  159 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  160 00000188 00000000        DCD              UART7_IRQHandler ; UART7
  161 0000018C 00000000        DCD              UART8_IRQHandler ; UART8
  162 00000190 00000000        DCD              SPI4_IRQHandler ; SPI4
  163 00000194 00000000        DCD              SPI5_IRQHandler ; SPI5
  164 00000198 00000000        DCD              SPI6_IRQHandler ; SPI6
  165 0000019C 00000000        DCD              SAI1_IRQHandler ; SAI1
  166 000001A0 00000000        DCD              LTDC_IRQHandler ; LTDC
  167 000001A4 00000000        DCD              LTDC_ER_IRQHandler ; LTDC error
                                                            
  168 000001A8 00000000        DCD              DMA2D_IRQHandler ; DMA2D
  169 000001AC 00000000        DCD              SAI2_IRQHandler ; SAI2
  170 000001B0 00000000        DCD              QUADSPI_IRQHandler ; QUADSPI
  171 000001B4 00000000        DCD              LPTIM1_IRQHandler ; LPTIM1
  172 000001B8 00000000        DCD              CEC_IRQHandler ; HDMI_CEC
  173 000001BC 00000000        DCD              I2C4_EV_IRQHandler ; I2C4 Event
                                                                               
                                                                      
  174 000001C0 00000000        DCD              I2C4_ER_IRQHandler 
                                                            ; I2C4 Error 
  175 000001C4 00000000        DCD              SPDIF_RX_IRQHandler ; SPDIF_RX
  176 000001C8 00000000        DCD              OTG_FS_EP1_OUT_IRQHandler ; USB
                                                             OTG FS End Point 1
                                                             Out               
                                                                   
  177 000001CC 00000000        DCD              OTG_FS_EP1_IN_IRQHandler ; USB 
                                                            OTG FS End Point 1 
                                                            In                 
                                                                  
  178 000001D0 00000000        DCD              OTG_FS_WKUP_IRQHandler ; USB OT
                                                            G FS Wakeup through
                                                             EXTI              
                                                                       
  179 000001D4 00000000        DCD              OTG_FS_IRQHandler ; USB OTG FS 
                                                                            
  180 000001D8 00000000        DCD              DMAMUX1_OVR_IRQHandler ; DMAMUX
                                                            1 Overrun interrupt
                                                              
  181 000001DC 00000000        DCD              HRTIM1_Master_IRQHandler ;  HRT



ARM Macro Assembler    Page 8 


                                                            IM Master Timer glo
                                                            bal Interrupts     
                                                                               
                                                                  
  182 000001E0 00000000        DCD              HRTIM1_TIMA_IRQHandler ;  HRTIM
                                                             Timer A global Int
                                                            errupt             
                                                                               
                                                                
  183 000001E4 00000000        DCD              HRTIM1_TIMB_IRQHandler ;  HRTIM
                                                             Timer B global Int
                                                            errupt             
                                                                               
                                                                
  184 000001E8 00000000        DCD              HRTIM1_TIMC_IRQHandler ;  HRTIM
                                                             Timer C global Int
                                                            errupt             
                                                                               
                                                                
  185 000001EC 00000000        DCD              HRTIM1_TIMD_IRQHandler ;  HRTIM
                                                             Timer D global Int
                                                            errupt             
                                                                               
                                                                
  186 000001F0 00000000        DCD              HRTIM1_TIME_IRQHandler ;  HRTIM
                                                             Timer E global Int
                                                            errupt             
                                                                               
                                                                
  187 000001F4 00000000        DCD              HRTIM1_FLT_IRQHandler ;  HRTIM 
                                                            Fault global Interr
                                                            upt 
  188 000001F8 00000000        DCD              DFSDM1_FLT0_IRQHandler ; DFSDM 
                                                            Filter0 Interrupt  
                                                             
  189 000001FC 00000000        DCD              DFSDM1_FLT1_IRQHandler ; DFSDM 
                                                            Filter1 Interrupt  
                                                                               
                                                                               
                                                                
  190 00000200 00000000        DCD              DFSDM1_FLT2_IRQHandler ; DFSDM 
                                                            Filter2 Interrupt  
                                                                               
                                                                               
                                                                
  191 00000204 00000000        DCD              DFSDM1_FLT3_IRQHandler ; DFSDM 
                                                            Filter3 Interrupt  
                                                                               
                                                                               
                                                                               
                                                                               
                                                                  
  192 00000208 00000000        DCD              SAI3_IRQHandler ;  SAI3 global 
                                                            Interrupt          
                                                                               
                                                                            
  193 0000020C 00000000        DCD              SWPMI1_IRQHandler ;  Serial Wir
                                                            e Interface 1 globa
                                                            l interrupt        



ARM Macro Assembler    Page 9 


                                                                              
  194 00000210 00000000        DCD              TIM15_IRQHandler ;  TIM15 globa
                                                            l Interrupt        
                                                                               
                                                                             
  195 00000214 00000000        DCD              TIM16_IRQHandler ;  TIM16 globa
                                                            l Interrupt        
                                                                               
                                                                             
  196 00000218 00000000        DCD              TIM17_IRQHandler ;  TIM17 globa
                                                            l Interrupt        
                                                                               
                                                                             
  197 0000021C 00000000        DCD              MDIOS_WKUP_IRQHandler ;  MDIOS 
                                                            Wakeup  Interrupt  
                                                                               
                                                                               
                                                               
  198 00000220 00000000        DCD              MDIOS_IRQHandler ;  MDIOS globa
                                                            l Interrupt        
                                                                               
                                                                             
  199 00000224 00000000        DCD              JPEG_IRQHandler ;  JPEG global 
                                                            Interrupt          
                                                                               
                                                                            
  200 00000228 00000000        DCD              MDMA_IRQHandler ;  MDMA global 
                                                            Interrupt          
                                                                               
                                                                            
  201 0000022C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                              
  202 00000230 00000000        DCD              SDMMC2_IRQHandler ;  SDMMC2 glo
                                                            bal Interrupt      
                                                                               
                                                                              
  203 00000234 00000000        DCD              HSEM1_IRQHandler ;  HSEM1 globa
                                                            l Interrupt        
                                                                               
                                                                              
  204 00000238 00000000        DCD              HSEM2_IRQHandler ;  HSEM2 globa
                                                            l Interrupt        
                                                                               
                                                                              
  205 0000023C 00000000        DCD              ADC3_IRQHandler ;  ADC3 global 
                                                            Interrupt          
                                                                               
                                                                             
  206 00000240 00000000        DCD              DMAMUX2_OVR_IRQHandler ; DMAMUX
                                                             Overrun interrupt 
                                                                               
                                                                               
                                                                
  207 00000244 00000000        DCD              BDMA_Channel0_IRQHandler ;  BDM
                                                            A Channel 0 global 
                                                            Interrupt          
                                                                               
                                                                   



ARM Macro Assembler    Page 10 


  208 00000248 00000000        DCD              BDMA_Channel1_IRQHandler ;  BDM
                                                            A Channel 1 global 
                                                            Interrupt          
                                                                               
                                                                   
  209 0000024C 00000000        DCD              BDMA_Channel2_IRQHandler ;  BDM
                                                            A Channel 2 global 
                                                            Interrupt          
                                                                               
                                                                   
  210 00000250 00000000        DCD              BDMA_Channel3_IRQHandler ;  BDM
                                                            A Channel 3 global 
                                                            Interrupt          
                                                                               
                                                                   
  211 00000254 00000000        DCD              BDMA_Channel4_IRQHandler ;  BDM
                                                            A Channel 4 global 
                                                            Interrupt          
                                                                               
                                                                   
  212 00000258 00000000        DCD              BDMA_Channel5_IRQHandler ;  BDM
                                                            A Channel 5 global 
                                                            Interrupt          
                                                                               
                                                                   
  213 0000025C 00000000        DCD              BDMA_Channel6_IRQHandler ;  BDM
                                                            A Channel 6 global 
                                                            Interrupt          
                                                                               
                                                                   
  214 00000260 00000000        DCD              BDMA_Channel7_IRQHandler ;  BDM
                                                            A Channel 7 global 
                                                            Interrupt          
                                                                               
                                                                   
  215 00000264 00000000        DCD              COMP1_IRQHandler ;  COMP1 globa
                                                            l Interrupt        
                                                                               
                                                                             
  216 00000268 00000000        DCD              LPTIM2_IRQHandler ;  LP TIM2 gl
                                                            obal interrupt     
                                                                               
                                                                              
  217 0000026C 00000000        DCD              LPTIM3_IRQHandler ;  LP TIM3 gl
                                                            obal interrupt     
                                                                               
                                                                              
  218 00000270 00000000        DCD              LPTIM4_IRQHandler ;  LP TIM4 gl
                                                            obal interrupt     
                                                                               
                                                                              
  219 00000274 00000000        DCD              LPTIM5_IRQHandler ;  LP TIM5 gl
                                                            obal interrupt     
                                                                               
                                                                              
  220 00000278 00000000        DCD              LPUART1_IRQHandler ;  LP UART1 
                                                            interrupt          
                                                                               
                                                                               



ARM Macro Assembler    Page 11 


                                                            
  221 0000027C 00000000        DCD              WWDG_RST_IRQHandler ; Window Wa
                                                            tchdog reset interr
                                                            upt (exti_d2_wwdg_i
                                                            t, exti_d1_wwdg_it)
                                                                               
                                                                         
  222 00000280 00000000        DCD              CRS_IRQHandler ;  Clock Recover
                                                            y Global Interrupt 
                                                                               
                                                                           
  223 00000284 00000000        DCD              ECC_IRQHandler ; ECC diagnostic
                                                             Global Interrupt  
                                                                               
                                                                               
                                                                 
  224 00000288 00000000        DCD              SAI4_IRQHandler ;  SAI4 global 
                                                            interrupt          
                                                                               
                                                                               
                                                            
  225 0000028C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                
  226 00000290 00000000        DCD              HOLD_CORE_IRQHandler ;  Hold co
                                                            re interrupt       
                                                                               
                                                                     
  227 00000294 00000000        DCD              WAKEUP_PIN_IRQHandler ;  Interr
                                                            upt for all 6 wake-
                                                            up pins 
  228 00000298         
  229 00000298         
  230 00000298         __Vectors_End
  231 00000298         
  232 00000298 00000298 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  233 00000298         
  234 00000298                 AREA             |.text|, CODE, READONLY
  235 00000000         
  236 00000000         ; Reset handler
  237 00000000         Reset_Handler
                               PROC
  238 00000000                 EXPORT           Reset_Handler                  
  [WEAK]
  239 00000000                 IMPORT           SystemInit
  240 00000000                 IMPORT           __main
  241 00000000         
  242 00000000 4809            LDR              R0, =SystemInit
  243 00000002 4780            BLX              R0
  244 00000004 4809            LDR              R0, =__main
  245 00000006 4700            BX               R0
  246 00000008                 ENDP
  247 00000008         
  248 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  249 00000008         
  250 00000008         NMI_Handler



ARM Macro Assembler    Page 12 


                               PROC
  251 00000008                 EXPORT           NMI_Handler                    
  [WEAK]
  252 00000008 E7FE            B                .
  253 0000000A                 ENDP
  255 0000000A         HardFault_Handler
                               PROC
  256 0000000A                 EXPORT           HardFault_Handler              
  [WEAK]
  257 0000000A E7FE            B                .
  258 0000000C                 ENDP
  260 0000000C         MemManage_Handler
                               PROC
  261 0000000C                 EXPORT           MemManage_Handler              
  [WEAK]
  262 0000000C E7FE            B                .
  263 0000000E                 ENDP
  265 0000000E         BusFault_Handler
                               PROC
  266 0000000E                 EXPORT           BusFault_Handler               
  [WEAK]
  267 0000000E E7FE            B                .
  268 00000010                 ENDP
  270 00000010         UsageFault_Handler
                               PROC
  271 00000010                 EXPORT           UsageFault_Handler             
  [WEAK]
  272 00000010 E7FE            B                .
  273 00000012                 ENDP
  274 00000012         SVC_Handler
                               PROC
  275 00000012                 EXPORT           SVC_Handler                    
  [WEAK]
  276 00000012 E7FE            B                .
  277 00000014                 ENDP
  279 00000014         DebugMon_Handler
                               PROC
  280 00000014                 EXPORT           DebugMon_Handler               
   [WEAK]
  281 00000014 E7FE            B                .
  282 00000016                 ENDP
  283 00000016         PendSV_Handler
                               PROC
  284 00000016                 EXPORT           PendSV_Handler                 
   [WEAK]
  285 00000016 E7FE            B                .
  286 00000018                 ENDP
  287 00000018         SysTick_Handler
                               PROC
  288 00000018                 EXPORT           SysTick_Handler                
   [WEAK]
  289 00000018 E7FE            B                .
  290 0000001A                 ENDP
  291 0000001A         
  292 0000001A         Default_Handler
                               PROC
  293 0000001A         
  294 0000001A                 EXPORT           WWDG_IRQHandler                
   [WEAK]



ARM Macro Assembler    Page 13 


  295 0000001A                 EXPORT           PVD_AVD_IRQHandler             
   [WEAK]
  296 0000001A                 EXPORT           TAMP_STAMP_IRQHandler          
   [WEAK]
  297 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  298 0000001A                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  299 0000001A                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  300 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  301 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  302 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  303 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  304 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  305 0000001A                 EXPORT           DMA1_Stream0_IRQHandler        
   [WEAK]
  306 0000001A                 EXPORT           DMA1_Stream1_IRQHandler        
   [WEAK]
  307 0000001A                 EXPORT           DMA1_Stream2_IRQHandler        
   [WEAK]
  308 0000001A                 EXPORT           DMA1_Stream3_IRQHandler        
   [WEAK]
  309 0000001A                 EXPORT           DMA1_Stream4_IRQHandler        
   [WEAK]
  310 0000001A                 EXPORT           DMA1_Stream5_IRQHandler        
   [WEAK]
  311 0000001A                 EXPORT           DMA1_Stream6_IRQHandler        
   [WEAK]
  312 0000001A                 EXPORT           DMA1_Stream7_IRQHandler        
   [WEAK]
  313 0000001A                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  314 0000001A                 EXPORT           FDCAN1_IT0_IRQHandler          
   [WEAK]
  315 0000001A                 EXPORT           FDCAN2_IT0_IRQHandler          
   [WEAK]
  316 0000001A                 EXPORT           FDCAN1_IT1_IRQHandler          
   [WEAK]
  317 0000001A                 EXPORT           FDCAN2_IT1_IRQHandler          
   [WEAK]
  318 0000001A                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  319 0000001A                 EXPORT           TIM1_BRK_IRQHandler            
   [WEAK]
  320 0000001A                 EXPORT           TIM1_UP_IRQHandler             
   [WEAK]
  321 0000001A                 EXPORT           TIM1_TRG_COM_IRQHandler        
   [WEAK]
  322 0000001A                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  323 0000001A                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  324 0000001A                 EXPORT           TIM3_IRQHandler                



ARM Macro Assembler    Page 14 


   [WEAK]
  325 0000001A                 EXPORT           TIM4_IRQHandler                
   [WEAK]
  326 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  327 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  328 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  329 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  330 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  331 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  332 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  333 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  334 0000001A                 EXPORT           USART3_IRQHandler              
   [WEAK]
  335 0000001A                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  336 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  337 0000001A                 EXPORT           TIM8_BRK_TIM12_IRQHandler      
   [WEAK]
  338 0000001A                 EXPORT           TIM8_UP_TIM13_IRQHandler       
   [WEAK]
  339 0000001A                 EXPORT           TIM8_TRG_COM_TIM14_IRQHandler  
   [WEAK]
  340 0000001A                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  341 0000001A                 EXPORT           DMA1_Stream7_IRQHandler        
   [WEAK]
  342 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  343 0000001A                 EXPORT           SDMMC1_IRQHandler              
   [WEAK]
  344 0000001A                 EXPORT           TIM5_IRQHandler                
   [WEAK]
  345 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  346 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  347 0000001A                 EXPORT           UART5_IRQHandler               
   [WEAK]
  348 0000001A                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  349 0000001A                 EXPORT           TIM7_IRQHandler                
   [WEAK]
  350 0000001A                 EXPORT           DMA2_Stream0_IRQHandler        
   [WEAK]
  351 0000001A                 EXPORT           DMA2_Stream1_IRQHandler        
   [WEAK]
  352 0000001A                 EXPORT           DMA2_Stream2_IRQHandler        
   [WEAK]
  353 0000001A                 EXPORT           DMA2_Stream3_IRQHandler        
   [WEAK]



ARM Macro Assembler    Page 15 


  354 0000001A                 EXPORT           DMA2_Stream4_IRQHandler        
   [WEAK]
  355 0000001A                 EXPORT           ETH_IRQHandler                 
   [WEAK]
  356 0000001A                 EXPORT           ETH_WKUP_IRQHandler            
   [WEAK]
  357 0000001A                 EXPORT           FDCAN_CAL_IRQHandler           
   [WEAK]
  358 0000001A                 EXPORT           CM7_SEV_IRQHandler             
   [WEAK]
  359 0000001A                 EXPORT           CM4_SEV_IRQHandler             
   [WEAK]
  360 0000001A                 EXPORT           DMA2_Stream5_IRQHandler        
   [WEAK]
  361 0000001A                 EXPORT           DMA2_Stream6_IRQHandler        
   [WEAK]
  362 0000001A                 EXPORT           DMA2_Stream7_IRQHandler        
   [WEAK]
  363 0000001A                 EXPORT           USART6_IRQHandler              
   [WEAK]
  364 0000001A                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  365 0000001A                 EXPORT           I2C3_ER_IRQHandler             
   [WEAK]
  366 0000001A                 EXPORT           OTG_HS_EP1_OUT_IRQHandler      
   [WEAK]
  367 0000001A                 EXPORT           OTG_HS_EP1_IN_IRQHandler       
   [WEAK]
  368 0000001A                 EXPORT           OTG_HS_WKUP_IRQHandler         
   [WEAK]
  369 0000001A                 EXPORT           OTG_HS_IRQHandler              
   [WEAK]
  370 0000001A                 EXPORT           DCMI_IRQHandler                
   [WEAK]
  371 0000001A                 EXPORT           RNG_IRQHandler                 
   [WEAK]
  372 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  373 0000001A                 EXPORT           UART7_IRQHandler               
   [WEAK]
  374 0000001A                 EXPORT           UART8_IRQHandler               
   [WEAK]
  375 0000001A                 EXPORT           SPI4_IRQHandler                
   [WEAK]
  376 0000001A                 EXPORT           SPI5_IRQHandler                
   [WEAK]
  377 0000001A                 EXPORT           SPI6_IRQHandler                
   [WEAK]
  378 0000001A                 EXPORT           SAI1_IRQHandler                
   [WEAK]
  379 0000001A                 EXPORT           LTDC_IRQHandler                
   [WEAK]
  380 0000001A                 EXPORT           LTDC_ER_IRQHandler             
   [WEAK]
  381 0000001A                 EXPORT           DMA2D_IRQHandler               
   [WEAK]
  382 0000001A                 EXPORT           SAI2_IRQHandler                
   [WEAK]
  383 0000001A                 EXPORT           QUADSPI_IRQHandler             



ARM Macro Assembler    Page 16 


   [WEAK]
  384 0000001A                 EXPORT           LPTIM1_IRQHandler              
   [WEAK]
  385 0000001A                 EXPORT           CEC_IRQHandler                 
   [WEAK]
  386 0000001A                 EXPORT           I2C4_EV_IRQHandler             
   [WEAK]
  387 0000001A                 EXPORT           I2C4_ER_IRQHandler             
   [WEAK]
  388 0000001A                 EXPORT           SPDIF_RX_IRQHandler            
   [WEAK]
  389 0000001A                 EXPORT           OTG_FS_EP1_OUT_IRQHandler      
   [WEAK]
  390 0000001A                 EXPORT           OTG_FS_EP1_IN_IRQHandler       
   [WEAK]
  391 0000001A                 EXPORT           OTG_FS_WKUP_IRQHandler         
   [WEAK]
  392 0000001A                 EXPORT           OTG_FS_IRQHandler              
   [WEAK]
  393 0000001A                 EXPORT           DMAMUX1_OVR_IRQHandler         
   [WEAK]
  394 0000001A                 EXPORT           HRTIM1_Master_IRQHandler       
   [WEAK]
  395 0000001A                 EXPORT           HRTIM1_TIMA_IRQHandler         
   [WEAK]
  396 0000001A                 EXPORT           HRTIM1_TIMB_IRQHandler         
   [WEAK]
  397 0000001A                 EXPORT           HRTIM1_TIMC_IRQHandler         
   [WEAK]
  398 0000001A                 EXPORT           HRTIM1_TIMD_IRQHandler         
   [WEAK]
  399 0000001A                 EXPORT           HRTIM1_TIME_IRQHandler         
   [WEAK]
  400 0000001A                 EXPORT           HRTIM1_FLT_IRQHandler          
   [WEAK]
  401 0000001A                 EXPORT           DFSDM1_FLT0_IRQHandler         
   [WEAK]
  402 0000001A                 EXPORT           DFSDM1_FLT1_IRQHandler         
   [WEAK]
  403 0000001A                 EXPORT           DFSDM1_FLT2_IRQHandler         
   [WEAK]
  404 0000001A                 EXPORT           DFSDM1_FLT3_IRQHandler         
   [WEAK]
  405 0000001A                 EXPORT           SAI3_IRQHandler                
   [WEAK]
  406 0000001A                 EXPORT           SWPMI1_IRQHandler              
   [WEAK]
  407 0000001A                 EXPORT           TIM15_IRQHandler               
   [WEAK]
  408 0000001A                 EXPORT           TIM16_IRQHandler               
   [WEAK]
  409 0000001A                 EXPORT           TIM17_IRQHandler               
   [WEAK]
  410 0000001A                 EXPORT           MDIOS_WKUP_IRQHandler          
   [WEAK]
  411 0000001A                 EXPORT           MDIOS_IRQHandler               
   [WEAK]
  412 0000001A                 EXPORT           JPEG_IRQHandler                
   [WEAK]



ARM Macro Assembler    Page 17 


  413 0000001A                 EXPORT           MDMA_IRQHandler                
   [WEAK]
  414 0000001A                 EXPORT           SDMMC2_IRQHandler              
   [WEAK]
  415 0000001A                 EXPORT           HSEM1_IRQHandler               
   [WEAK]
  416 0000001A                 EXPORT           HSEM2_IRQHandler               
   [WEAK]
  417 0000001A                 EXPORT           ADC3_IRQHandler                
   [WEAK]
  418 0000001A                 EXPORT           DMAMUX2_OVR_IRQHandler         
   [WEAK]
  419 0000001A                 EXPORT           BDMA_Channel0_IRQHandler       
   [WEAK]
  420 0000001A                 EXPORT           BDMA_Channel1_IRQHandler       
   [WEAK]
  421 0000001A                 EXPORT           BDMA_Channel2_IRQHandler       
   [WEAK]
  422 0000001A                 EXPORT           BDMA_Channel3_IRQHandler       
   [WEAK]
  423 0000001A                 EXPORT           BDMA_Channel4_IRQHandler       
   [WEAK]
  424 0000001A                 EXPORT           BDMA_Channel5_IRQHandler       
   [WEAK]
  425 0000001A                 EXPORT           BDMA_Channel6_IRQHandler       
   [WEAK]
  426 0000001A                 EXPORT           BDMA_Channel7_IRQHandler       
   [WEAK]
  427 0000001A                 EXPORT           COMP1_IRQHandler               
   [WEAK]
  428 0000001A                 EXPORT           LPTIM2_IRQHandler              
   [WEAK]
  429 0000001A                 EXPORT           LPTIM3_IRQHandler              
   [WEAK]
  430 0000001A                 EXPORT           LPTIM4_IRQHandler              
   [WEAK]
  431 0000001A                 EXPORT           LPTIM5_IRQHandler              
   [WEAK]
  432 0000001A                 EXPORT           LPUART1_IRQHandler             
   [WEAK]
  433 0000001A                 EXPORT           WWDG_RST_IRQHandler            
   [WEAK]
  434 0000001A                 EXPORT           CRS_IRQHandler                 
   [WEAK]
  435 0000001A                 EXPORT           ECC_IRQHandler                 
   [WEAK]
  436 0000001A                 EXPORT           SAI4_IRQHandler                
   [WEAK]
  437 0000001A                 EXPORT           HOLD_CORE_IRQHandler           
   [WEAK]
  438 0000001A                 EXPORT           WAKEUP_PIN_IRQHandler          
   [WEAK]
  439 0000001A         
  440 0000001A         
  441 0000001A         WWDG_IRQHandler
  442 0000001A         PVD_AVD_IRQHandler
  443 0000001A         TAMP_STAMP_IRQHandler
  444 0000001A         RTC_WKUP_IRQHandler
  445 0000001A         FLASH_IRQHandler



ARM Macro Assembler    Page 18 


  446 0000001A         RCC_IRQHandler
  447 0000001A         EXTI0_IRQHandler
  448 0000001A         EXTI1_IRQHandler
  449 0000001A         EXTI2_IRQHandler
  450 0000001A         EXTI3_IRQHandler
  451 0000001A         EXTI4_IRQHandler
  452 0000001A         DMA1_Stream0_IRQHandler
  453 0000001A         DMA1_Stream1_IRQHandler
  454 0000001A         DMA1_Stream2_IRQHandler
  455 0000001A         DMA1_Stream3_IRQHandler
  456 0000001A         DMA1_Stream4_IRQHandler
  457 0000001A         DMA1_Stream5_IRQHandler
  458 0000001A         DMA1_Stream6_IRQHandler
  459 0000001A         ADC_IRQHandler
  460 0000001A         FDCAN1_IT0_IRQHandler
  461 0000001A         FDCAN2_IT0_IRQHandler
  462 0000001A         FDCAN1_IT1_IRQHandler
  463 0000001A         FDCAN2_IT1_IRQHandler
  464 0000001A         EXTI9_5_IRQHandler
  465 0000001A         TIM1_BRK_IRQHandler
  466 0000001A         TIM1_UP_IRQHandler
  467 0000001A         TIM1_TRG_COM_IRQHandler
  468 0000001A         TIM1_CC_IRQHandler
  469 0000001A         TIM2_IRQHandler
  470 0000001A         TIM3_IRQHandler
  471 0000001A         TIM4_IRQHandler
  472 0000001A         I2C1_EV_IRQHandler
  473 0000001A         I2C1_ER_IRQHandler
  474 0000001A         I2C2_EV_IRQHandler
  475 0000001A         I2C2_ER_IRQHandler
  476 0000001A         SPI1_IRQHandler
  477 0000001A         SPI2_IRQHandler
  478 0000001A         USART1_IRQHandler
  479 0000001A         USART2_IRQHandler
  480 0000001A         USART3_IRQHandler
  481 0000001A         EXTI15_10_IRQHandler
  482 0000001A         RTC_Alarm_IRQHandler
  483 0000001A         TIM8_BRK_TIM12_IRQHandler
  484 0000001A         TIM8_UP_TIM13_IRQHandler
  485 0000001A         TIM8_TRG_COM_TIM14_IRQHandler
  486 0000001A         TIM8_CC_IRQHandler
  487 0000001A         DMA1_Stream7_IRQHandler
  488 0000001A         FMC_IRQHandler
  489 0000001A         SDMMC1_IRQHandler
  490 0000001A         TIM5_IRQHandler
  491 0000001A         SPI3_IRQHandler
  492 0000001A         UART4_IRQHandler
  493 0000001A         UART5_IRQHandler
  494 0000001A         TIM6_DAC_IRQHandler
  495 0000001A         TIM7_IRQHandler
  496 0000001A         DMA2_Stream0_IRQHandler
  497 0000001A         DMA2_Stream1_IRQHandler
  498 0000001A         DMA2_Stream2_IRQHandler
  499 0000001A         DMA2_Stream3_IRQHandler
  500 0000001A         DMA2_Stream4_IRQHandler
  501 0000001A         ETH_IRQHandler
  502 0000001A         ETH_WKUP_IRQHandler
  503 0000001A         FDCAN_CAL_IRQHandler
  504 0000001A         CM7_SEV_IRQHandler



ARM Macro Assembler    Page 19 


  505 0000001A         CM4_SEV_IRQHandler
  506 0000001A         DMA2_Stream5_IRQHandler
  507 0000001A         DMA2_Stream6_IRQHandler
  508 0000001A         DMA2_Stream7_IRQHandler
  509 0000001A         USART6_IRQHandler
  510 0000001A         I2C3_EV_IRQHandler
  511 0000001A         I2C3_ER_IRQHandler
  512 0000001A         OTG_HS_EP1_OUT_IRQHandler
  513 0000001A         OTG_HS_EP1_IN_IRQHandler
  514 0000001A         OTG_HS_WKUP_IRQHandler
  515 0000001A         OTG_HS_IRQHandler
  516 0000001A         DCMI_IRQHandler
  517 0000001A         RNG_IRQHandler
  518 0000001A         FPU_IRQHandler
  519 0000001A         UART7_IRQHandler
  520 0000001A         UART8_IRQHandler
  521 0000001A         SPI4_IRQHandler
  522 0000001A         SPI5_IRQHandler
  523 0000001A         SPI6_IRQHandler
  524 0000001A         SAI1_IRQHandler
  525 0000001A         LTDC_IRQHandler
  526 0000001A         LTDC_ER_IRQHandler
  527 0000001A         DMA2D_IRQHandler
  528 0000001A         SAI2_IRQHandler
  529 0000001A         QUADSPI_IRQHandler
  530 0000001A         LPTIM1_IRQHandler
  531 0000001A         CEC_IRQHandler
  532 0000001A         I2C4_EV_IRQHandler
  533 0000001A         I2C4_ER_IRQHandler
  534 0000001A         SPDIF_RX_IRQHandler
  535 0000001A         OTG_FS_EP1_OUT_IRQHandler
  536 0000001A         OTG_FS_EP1_IN_IRQHandler
  537 0000001A         OTG_FS_WKUP_IRQHandler
  538 0000001A         OTG_FS_IRQHandler
  539 0000001A         DMAMUX1_OVR_IRQHandler
  540 0000001A         HRTIM1_Master_IRQHandler
  541 0000001A         HRTIM1_TIMA_IRQHandler
  542 0000001A         HRTIM1_TIMB_IRQHandler
  543 0000001A         HRTIM1_TIMC_IRQHandler
  544 0000001A         HRTIM1_TIMD_IRQHandler
  545 0000001A         HRTIM1_TIME_IRQHandler
  546 0000001A         HRTIM1_FLT_IRQHandler
  547 0000001A         DFSDM1_FLT0_IRQHandler
  548 0000001A         DFSDM1_FLT1_IRQHandler
  549 0000001A         DFSDM1_FLT2_IRQHandler
  550 0000001A         DFSDM1_FLT3_IRQHandler
  551 0000001A         SAI3_IRQHandler
  552 0000001A         SWPMI1_IRQHandler
  553 0000001A         TIM15_IRQHandler
  554 0000001A         TIM16_IRQHandler
  555 0000001A         TIM17_IRQHandler
  556 0000001A         MDIOS_WKUP_IRQHandler
  557 0000001A         MDIOS_IRQHandler
  558 0000001A         JPEG_IRQHandler
  559 0000001A         MDMA_IRQHandler
  560 0000001A         SDMMC2_IRQHandler
  561 0000001A         HSEM1_IRQHandler
  562 0000001A         HSEM2_IRQHandler
  563 0000001A         ADC3_IRQHandler



ARM Macro Assembler    Page 20 


  564 0000001A         DMAMUX2_OVR_IRQHandler
  565 0000001A         BDMA_Channel0_IRQHandler
  566 0000001A         BDMA_Channel1_IRQHandler
  567 0000001A         BDMA_Channel2_IRQHandler
  568 0000001A         BDMA_Channel3_IRQHandler
  569 0000001A         BDMA_Channel4_IRQHandler
  570 0000001A         BDMA_Channel5_IRQHandler
  571 0000001A         BDMA_Channel6_IRQHandler
  572 0000001A         BDMA_Channel7_IRQHandler
  573 0000001A         COMP1_IRQHandler
  574 0000001A         LPTIM2_IRQHandler
  575 0000001A         LPTIM3_IRQHandler
  576 0000001A         LPTIM4_IRQHandler
  577 0000001A         LPTIM5_IRQHandler
  578 0000001A         LPUART1_IRQHandler
  579 0000001A         WWDG_RST_IRQHandler
  580 0000001A         CRS_IRQHandler
  581 0000001A         ECC_IRQHandler
  582 0000001A         SAI4_IRQHandler
  583 0000001A         HOLD_CORE_IRQHandler
  584 0000001A         WAKEUP_PIN_IRQHandler
  585 0000001A         
  586 0000001A E7FE            B                .
  587 0000001C         
  588 0000001C                 ENDP
  589 0000001C         
  590 0000001C                 ALIGN
  591 0000001C         
  592 0000001C         ;*******************************************************
                       ************************
  593 0000001C         ; User Stack and Heap initialization
  594 0000001C         ;*******************************************************
                       ************************
  595 0000001C                 IF               :DEF:__MICROLIB
  602 0000001C         
  603 0000001C                 IMPORT           __use_two_region_memory
  604 0000001C                 EXPORT           __user_initial_stackheap
  605 0000001C         
  606 0000001C         __user_initial_stackheap
  607 0000001C         
  608 0000001C 4804            LDR              R0, =  Heap_Mem
  609 0000001E 4905            LDR              R1, =(Stack_Mem + Stack_Size)
  610 00000020 4A05            LDR              R2, = (Heap_Mem +  Heap_Size)
  611 00000022 4B06            LDR              R3, = Stack_Mem
  612 00000024 4770            BX               LR
  613 00000026         
  614 00000026 00 00           ALIGN
  615 00000028         
  616 00000028                 ENDIF
  617 00000028         
  618 00000028                 END
              00000000 
              00000000 
              00000000 
              00000400 
              00000200 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M7.fp.dp --apcs=
interwork --depend=lcd_cm7\startup_stm32h745xx_cm7.d -olcd_cm7\startup_stm32h74



ARM Macro Assembler    Page 21 


5xx_cm7.o -I.\RTE\_LCD_CM7 -ID:\keilc51\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Inc
lude --predefine="__UVISION_VERSION SETA 542" --predefine="CORE_CM7 SETA 1" --p
redefine="STM32H745xx SETA 1" --predefine="_RTE_ SETA 1" --list=startup_stm32h7
45xx_cm7.lst startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 34 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 35 in file startup_stm32h745xx_CM7.s
   Uses
      At line 609 in file startup_stm32h745xx_CM7.s
      At line 611 in file startup_stm32h745xx_CM7.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 36 in file startup_stm32h745xx_CM7.s
   Uses
      At line 60 in file startup_stm32h745xx_CM7.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 45 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 47 in file startup_stm32h745xx_CM7.s
   Uses
      At line 608 in file startup_stm32h745xx_CM7.s
      At line 610 in file startup_stm32h745xx_CM7.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 46 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 48 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 55 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 60 in file startup_stm32h745xx_CM7.s
   Uses
      At line 56 in file startup_stm32h745xx_CM7.s
      At line 232 in file startup_stm32h745xx_CM7.s

__Vectors_End 00000298

Symbol: __Vectors_End
   Definitions
      At line 230 in file startup_stm32h745xx_CM7.s
   Uses
      At line 57 in file startup_stm32h745xx_CM7.s
      At line 232 in file startup_stm32h745xx_CM7.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 234 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: .text unused
ADC3_IRQHandler 0000001A

Symbol: ADC3_IRQHandler
   Definitions
      At line 563 in file startup_stm32h745xx_CM7.s
   Uses
      At line 205 in file startup_stm32h745xx_CM7.s
      At line 417 in file startup_stm32h745xx_CM7.s

ADC_IRQHandler 0000001A

Symbol: ADC_IRQHandler
   Definitions
      At line 459 in file startup_stm32h745xx_CM7.s
   Uses
      At line 96 in file startup_stm32h745xx_CM7.s
      At line 313 in file startup_stm32h745xx_CM7.s

BDMA_Channel0_IRQHandler 0000001A

Symbol: BDMA_Channel0_IRQHandler
   Definitions
      At line 565 in file startup_stm32h745xx_CM7.s
   Uses
      At line 207 in file startup_stm32h745xx_CM7.s
      At line 419 in file startup_stm32h745xx_CM7.s

BDMA_Channel1_IRQHandler 0000001A

Symbol: BDMA_Channel1_IRQHandler
   Definitions
      At line 566 in file startup_stm32h745xx_CM7.s
   Uses
      At line 208 in file startup_stm32h745xx_CM7.s
      At line 420 in file startup_stm32h745xx_CM7.s

BDMA_Channel2_IRQHandler 0000001A

Symbol: BDMA_Channel2_IRQHandler
   Definitions
      At line 567 in file startup_stm32h745xx_CM7.s
   Uses
      At line 209 in file startup_stm32h745xx_CM7.s
      At line 421 in file startup_stm32h745xx_CM7.s

BDMA_Channel3_IRQHandler 0000001A

Symbol: BDMA_Channel3_IRQHandler
   Definitions
      At line 568 in file startup_stm32h745xx_CM7.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 210 in file startup_stm32h745xx_CM7.s
      At line 422 in file startup_stm32h745xx_CM7.s

BDMA_Channel4_IRQHandler 0000001A

Symbol: BDMA_Channel4_IRQHandler
   Definitions
      At line 569 in file startup_stm32h745xx_CM7.s
   Uses
      At line 211 in file startup_stm32h745xx_CM7.s
      At line 423 in file startup_stm32h745xx_CM7.s

BDMA_Channel5_IRQHandler 0000001A

Symbol: BDMA_Channel5_IRQHandler
   Definitions
      At line 570 in file startup_stm32h745xx_CM7.s
   Uses
      At line 212 in file startup_stm32h745xx_CM7.s
      At line 424 in file startup_stm32h745xx_CM7.s

BDMA_Channel6_IRQHandler 0000001A

Symbol: BDMA_Channel6_IRQHandler
   Definitions
      At line 571 in file startup_stm32h745xx_CM7.s
   Uses
      At line 213 in file startup_stm32h745xx_CM7.s
      At line 425 in file startup_stm32h745xx_CM7.s

BDMA_Channel7_IRQHandler 0000001A

Symbol: BDMA_Channel7_IRQHandler
   Definitions
      At line 572 in file startup_stm32h745xx_CM7.s
   Uses
      At line 214 in file startup_stm32h745xx_CM7.s
      At line 426 in file startup_stm32h745xx_CM7.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 265 in file startup_stm32h745xx_CM7.s
   Uses
      At line 65 in file startup_stm32h745xx_CM7.s
      At line 266 in file startup_stm32h745xx_CM7.s

CEC_IRQHandler 0000001A

Symbol: CEC_IRQHandler
   Definitions
      At line 531 in file startup_stm32h745xx_CM7.s
   Uses
      At line 172 in file startup_stm32h745xx_CM7.s
      At line 385 in file startup_stm32h745xx_CM7.s

CM4_SEV_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: CM4_SEV_IRQHandler
   Definitions
      At line 505 in file startup_stm32h745xx_CM7.s
   Uses
      At line 143 in file startup_stm32h745xx_CM7.s
      At line 359 in file startup_stm32h745xx_CM7.s

CM7_SEV_IRQHandler 0000001A

Symbol: CM7_SEV_IRQHandler
   Definitions
      At line 504 in file startup_stm32h745xx_CM7.s
   Uses
      At line 142 in file startup_stm32h745xx_CM7.s
      At line 358 in file startup_stm32h745xx_CM7.s

COMP1_IRQHandler 0000001A

Symbol: COMP1_IRQHandler
   Definitions
      At line 573 in file startup_stm32h745xx_CM7.s
   Uses
      At line 215 in file startup_stm32h745xx_CM7.s
      At line 427 in file startup_stm32h745xx_CM7.s

CRS_IRQHandler 0000001A

Symbol: CRS_IRQHandler
   Definitions
      At line 580 in file startup_stm32h745xx_CM7.s
   Uses
      At line 222 in file startup_stm32h745xx_CM7.s
      At line 434 in file startup_stm32h745xx_CM7.s

DCMI_IRQHandler 0000001A

Symbol: DCMI_IRQHandler
   Definitions
      At line 516 in file startup_stm32h745xx_CM7.s
   Uses
      At line 156 in file startup_stm32h745xx_CM7.s
      At line 370 in file startup_stm32h745xx_CM7.s

DFSDM1_FLT0_IRQHandler 0000001A

Symbol: DFSDM1_FLT0_IRQHandler
   Definitions
      At line 547 in file startup_stm32h745xx_CM7.s
   Uses
      At line 188 in file startup_stm32h745xx_CM7.s
      At line 401 in file startup_stm32h745xx_CM7.s

DFSDM1_FLT1_IRQHandler 0000001A

Symbol: DFSDM1_FLT1_IRQHandler
   Definitions
      At line 548 in file startup_stm32h745xx_CM7.s
   Uses
      At line 189 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 402 in file startup_stm32h745xx_CM7.s

DFSDM1_FLT2_IRQHandler 0000001A

Symbol: DFSDM1_FLT2_IRQHandler
   Definitions
      At line 549 in file startup_stm32h745xx_CM7.s
   Uses
      At line 190 in file startup_stm32h745xx_CM7.s
      At line 403 in file startup_stm32h745xx_CM7.s

DFSDM1_FLT3_IRQHandler 0000001A

Symbol: DFSDM1_FLT3_IRQHandler
   Definitions
      At line 550 in file startup_stm32h745xx_CM7.s
   Uses
      At line 191 in file startup_stm32h745xx_CM7.s
      At line 404 in file startup_stm32h745xx_CM7.s

DMA1_Stream0_IRQHandler 0000001A

Symbol: DMA1_Stream0_IRQHandler
   Definitions
      At line 452 in file startup_stm32h745xx_CM7.s
   Uses
      At line 89 in file startup_stm32h745xx_CM7.s
      At line 305 in file startup_stm32h745xx_CM7.s

DMA1_Stream1_IRQHandler 0000001A

Symbol: DMA1_Stream1_IRQHandler
   Definitions
      At line 453 in file startup_stm32h745xx_CM7.s
   Uses
      At line 90 in file startup_stm32h745xx_CM7.s
      At line 306 in file startup_stm32h745xx_CM7.s

DMA1_Stream2_IRQHandler 0000001A

Symbol: DMA1_Stream2_IRQHandler
   Definitions
      At line 454 in file startup_stm32h745xx_CM7.s
   Uses
      At line 91 in file startup_stm32h745xx_CM7.s
      At line 307 in file startup_stm32h745xx_CM7.s

DMA1_Stream3_IRQHandler 0000001A

Symbol: DMA1_Stream3_IRQHandler
   Definitions
      At line 455 in file startup_stm32h745xx_CM7.s
   Uses
      At line 92 in file startup_stm32h745xx_CM7.s
      At line 308 in file startup_stm32h745xx_CM7.s

DMA1_Stream4_IRQHandler 0000001A

Symbol: DMA1_Stream4_IRQHandler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 456 in file startup_stm32h745xx_CM7.s
   Uses
      At line 93 in file startup_stm32h745xx_CM7.s
      At line 309 in file startup_stm32h745xx_CM7.s

DMA1_Stream5_IRQHandler 0000001A

Symbol: DMA1_Stream5_IRQHandler
   Definitions
      At line 457 in file startup_stm32h745xx_CM7.s
   Uses
      At line 94 in file startup_stm32h745xx_CM7.s
      At line 310 in file startup_stm32h745xx_CM7.s

DMA1_Stream6_IRQHandler 0000001A

Symbol: DMA1_Stream6_IRQHandler
   Definitions
      At line 458 in file startup_stm32h745xx_CM7.s
   Uses
      At line 95 in file startup_stm32h745xx_CM7.s
      At line 311 in file startup_stm32h745xx_CM7.s

DMA1_Stream7_IRQHandler 0000001A

Symbol: DMA1_Stream7_IRQHandler
   Definitions
      At line 487 in file startup_stm32h745xx_CM7.s
   Uses
      At line 125 in file startup_stm32h745xx_CM7.s
      At line 312 in file startup_stm32h745xx_CM7.s
      At line 341 in file startup_stm32h745xx_CM7.s

DMA2D_IRQHandler 0000001A

Symbol: DMA2D_IRQHandler
   Definitions
      At line 527 in file startup_stm32h745xx_CM7.s
   Uses
      At line 168 in file startup_stm32h745xx_CM7.s
      At line 381 in file startup_stm32h745xx_CM7.s

DMA2_Stream0_IRQHandler 0000001A

Symbol: DMA2_Stream0_IRQHandler
   Definitions
      At line 496 in file startup_stm32h745xx_CM7.s
   Uses
      At line 134 in file startup_stm32h745xx_CM7.s
      At line 350 in file startup_stm32h745xx_CM7.s

DMA2_Stream1_IRQHandler 0000001A

Symbol: DMA2_Stream1_IRQHandler
   Definitions
      At line 497 in file startup_stm32h745xx_CM7.s
   Uses
      At line 135 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

      At line 351 in file startup_stm32h745xx_CM7.s

DMA2_Stream2_IRQHandler 0000001A

Symbol: DMA2_Stream2_IRQHandler
   Definitions
      At line 498 in file startup_stm32h745xx_CM7.s
   Uses
      At line 136 in file startup_stm32h745xx_CM7.s
      At line 352 in file startup_stm32h745xx_CM7.s

DMA2_Stream3_IRQHandler 0000001A

Symbol: DMA2_Stream3_IRQHandler
   Definitions
      At line 499 in file startup_stm32h745xx_CM7.s
   Uses
      At line 137 in file startup_stm32h745xx_CM7.s
      At line 353 in file startup_stm32h745xx_CM7.s

DMA2_Stream4_IRQHandler 0000001A

Symbol: DMA2_Stream4_IRQHandler
   Definitions
      At line 500 in file startup_stm32h745xx_CM7.s
   Uses
      At line 138 in file startup_stm32h745xx_CM7.s
      At line 354 in file startup_stm32h745xx_CM7.s

DMA2_Stream5_IRQHandler 0000001A

Symbol: DMA2_Stream5_IRQHandler
   Definitions
      At line 506 in file startup_stm32h745xx_CM7.s
   Uses
      At line 146 in file startup_stm32h745xx_CM7.s
      At line 360 in file startup_stm32h745xx_CM7.s

DMA2_Stream6_IRQHandler 0000001A

Symbol: DMA2_Stream6_IRQHandler
   Definitions
      At line 507 in file startup_stm32h745xx_CM7.s
   Uses
      At line 147 in file startup_stm32h745xx_CM7.s
      At line 361 in file startup_stm32h745xx_CM7.s

DMA2_Stream7_IRQHandler 0000001A

Symbol: DMA2_Stream7_IRQHandler
   Definitions
      At line 508 in file startup_stm32h745xx_CM7.s
   Uses
      At line 148 in file startup_stm32h745xx_CM7.s
      At line 362 in file startup_stm32h745xx_CM7.s

DMAMUX1_OVR_IRQHandler 0000001A

Symbol: DMAMUX1_OVR_IRQHandler



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 539 in file startup_stm32h745xx_CM7.s
   Uses
      At line 180 in file startup_stm32h745xx_CM7.s
      At line 393 in file startup_stm32h745xx_CM7.s

DMAMUX2_OVR_IRQHandler 0000001A

Symbol: DMAMUX2_OVR_IRQHandler
   Definitions
      At line 564 in file startup_stm32h745xx_CM7.s
   Uses
      At line 206 in file startup_stm32h745xx_CM7.s
      At line 418 in file startup_stm32h745xx_CM7.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 279 in file startup_stm32h745xx_CM7.s
   Uses
      At line 72 in file startup_stm32h745xx_CM7.s
      At line 280 in file startup_stm32h745xx_CM7.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 292 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: Default_Handler unused
ECC_IRQHandler 0000001A

Symbol: ECC_IRQHandler
   Definitions
      At line 581 in file startup_stm32h745xx_CM7.s
   Uses
      At line 223 in file startup_stm32h745xx_CM7.s
      At line 435 in file startup_stm32h745xx_CM7.s

ETH_IRQHandler 0000001A

Symbol: ETH_IRQHandler
   Definitions
      At line 501 in file startup_stm32h745xx_CM7.s
   Uses
      At line 139 in file startup_stm32h745xx_CM7.s
      At line 355 in file startup_stm32h745xx_CM7.s

ETH_WKUP_IRQHandler 0000001A

Symbol: ETH_WKUP_IRQHandler
   Definitions
      At line 502 in file startup_stm32h745xx_CM7.s
   Uses
      At line 140 in file startup_stm32h745xx_CM7.s
      At line 356 in file startup_stm32h745xx_CM7.s




ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols

EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 447 in file startup_stm32h745xx_CM7.s
   Uses
      At line 84 in file startup_stm32h745xx_CM7.s
      At line 300 in file startup_stm32h745xx_CM7.s

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 481 in file startup_stm32h745xx_CM7.s
   Uses
      At line 118 in file startup_stm32h745xx_CM7.s
      At line 335 in file startup_stm32h745xx_CM7.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 448 in file startup_stm32h745xx_CM7.s
   Uses
      At line 85 in file startup_stm32h745xx_CM7.s
      At line 301 in file startup_stm32h745xx_CM7.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 449 in file startup_stm32h745xx_CM7.s
   Uses
      At line 86 in file startup_stm32h745xx_CM7.s
      At line 302 in file startup_stm32h745xx_CM7.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 450 in file startup_stm32h745xx_CM7.s
   Uses
      At line 87 in file startup_stm32h745xx_CM7.s
      At line 303 in file startup_stm32h745xx_CM7.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 451 in file startup_stm32h745xx_CM7.s
   Uses
      At line 88 in file startup_stm32h745xx_CM7.s
      At line 304 in file startup_stm32h745xx_CM7.s

EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 464 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 101 in file startup_stm32h745xx_CM7.s
      At line 318 in file startup_stm32h745xx_CM7.s

FDCAN1_IT0_IRQHandler 0000001A

Symbol: FDCAN1_IT0_IRQHandler
   Definitions
      At line 460 in file startup_stm32h745xx_CM7.s
   Uses
      At line 97 in file startup_stm32h745xx_CM7.s
      At line 314 in file startup_stm32h745xx_CM7.s

FDCAN1_IT1_IRQHandler 0000001A

Symbol: FDCAN1_IT1_IRQHandler
   Definitions
      At line 462 in file startup_stm32h745xx_CM7.s
   Uses
      At line 99 in file startup_stm32h745xx_CM7.s
      At line 316 in file startup_stm32h745xx_CM7.s

FDCAN2_IT0_IRQHandler 0000001A

Symbol: FDCAN2_IT0_IRQHandler
   Definitions
      At line 461 in file startup_stm32h745xx_CM7.s
   Uses
      At line 98 in file startup_stm32h745xx_CM7.s
      At line 315 in file startup_stm32h745xx_CM7.s

FDCAN2_IT1_IRQHandler 0000001A

Symbol: FDCAN2_IT1_IRQHandler
   Definitions
      At line 463 in file startup_stm32h745xx_CM7.s
   Uses
      At line 100 in file startup_stm32h745xx_CM7.s
      At line 317 in file startup_stm32h745xx_CM7.s

FDCAN_CAL_IRQHandler 0000001A

Symbol: FDCAN_CAL_IRQHandler
   Definitions
      At line 503 in file startup_stm32h745xx_CM7.s
   Uses
      At line 141 in file startup_stm32h745xx_CM7.s
      At line 357 in file startup_stm32h745xx_CM7.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 445 in file startup_stm32h745xx_CM7.s
   Uses
      At line 82 in file startup_stm32h745xx_CM7.s
      At line 298 in file startup_stm32h745xx_CM7.s

FMC_IRQHandler 0000001A



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols


Symbol: FMC_IRQHandler
   Definitions
      At line 488 in file startup_stm32h745xx_CM7.s
   Uses
      At line 126 in file startup_stm32h745xx_CM7.s
      At line 342 in file startup_stm32h745xx_CM7.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 518 in file startup_stm32h745xx_CM7.s
   Uses
      At line 159 in file startup_stm32h745xx_CM7.s
      At line 372 in file startup_stm32h745xx_CM7.s

HOLD_CORE_IRQHandler 0000001A

Symbol: HOLD_CORE_IRQHandler
   Definitions
      At line 583 in file startup_stm32h745xx_CM7.s
   Uses
      At line 226 in file startup_stm32h745xx_CM7.s
      At line 437 in file startup_stm32h745xx_CM7.s

HRTIM1_FLT_IRQHandler 0000001A

Symbol: HRTIM1_FLT_IRQHandler
   Definitions
      At line 546 in file startup_stm32h745xx_CM7.s
   Uses
      At line 187 in file startup_stm32h745xx_CM7.s
      At line 400 in file startup_stm32h745xx_CM7.s

HRTIM1_Master_IRQHandler 0000001A

Symbol: HRTIM1_Master_IRQHandler
   Definitions
      At line 540 in file startup_stm32h745xx_CM7.s
   Uses
      At line 181 in file startup_stm32h745xx_CM7.s
      At line 394 in file startup_stm32h745xx_CM7.s

HRTIM1_TIMA_IRQHandler 0000001A

Symbol: HRTIM1_TIMA_IRQHandler
   Definitions
      At line 541 in file startup_stm32h745xx_CM7.s
   Uses
      At line 182 in file startup_stm32h745xx_CM7.s
      At line 395 in file startup_stm32h745xx_CM7.s

HRTIM1_TIMB_IRQHandler 0000001A

Symbol: HRTIM1_TIMB_IRQHandler
   Definitions
      At line 542 in file startup_stm32h745xx_CM7.s
   Uses



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 183 in file startup_stm32h745xx_CM7.s
      At line 396 in file startup_stm32h745xx_CM7.s

HRTIM1_TIMC_IRQHandler 0000001A

Symbol: HRTIM1_TIMC_IRQHandler
   Definitions
      At line 543 in file startup_stm32h745xx_CM7.s
   Uses
      At line 184 in file startup_stm32h745xx_CM7.s
      At line 397 in file startup_stm32h745xx_CM7.s

HRTIM1_TIMD_IRQHandler 0000001A

Symbol: HRTIM1_TIMD_IRQHandler
   Definitions
      At line 544 in file startup_stm32h745xx_CM7.s
   Uses
      At line 185 in file startup_stm32h745xx_CM7.s
      At line 398 in file startup_stm32h745xx_CM7.s

HRTIM1_TIME_IRQHandler 0000001A

Symbol: HRTIM1_TIME_IRQHandler
   Definitions
      At line 545 in file startup_stm32h745xx_CM7.s
   Uses
      At line 186 in file startup_stm32h745xx_CM7.s
      At line 399 in file startup_stm32h745xx_CM7.s

HSEM1_IRQHandler 0000001A

Symbol: HSEM1_IRQHandler
   Definitions
      At line 561 in file startup_stm32h745xx_CM7.s
   Uses
      At line 203 in file startup_stm32h745xx_CM7.s
      At line 415 in file startup_stm32h745xx_CM7.s

HSEM2_IRQHandler 0000001A

Symbol: HSEM2_IRQHandler
   Definitions
      At line 562 in file startup_stm32h745xx_CM7.s
   Uses
      At line 204 in file startup_stm32h745xx_CM7.s
      At line 416 in file startup_stm32h745xx_CM7.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 255 in file startup_stm32h745xx_CM7.s
   Uses
      At line 63 in file startup_stm32h745xx_CM7.s
      At line 256 in file startup_stm32h745xx_CM7.s

I2C1_ER_IRQHandler 0000001A




ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 473 in file startup_stm32h745xx_CM7.s
   Uses
      At line 110 in file startup_stm32h745xx_CM7.s
      At line 327 in file startup_stm32h745xx_CM7.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 472 in file startup_stm32h745xx_CM7.s
   Uses
      At line 109 in file startup_stm32h745xx_CM7.s
      At line 326 in file startup_stm32h745xx_CM7.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 475 in file startup_stm32h745xx_CM7.s
   Uses
      At line 112 in file startup_stm32h745xx_CM7.s
      At line 329 in file startup_stm32h745xx_CM7.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 474 in file startup_stm32h745xx_CM7.s
   Uses
      At line 111 in file startup_stm32h745xx_CM7.s
      At line 328 in file startup_stm32h745xx_CM7.s

I2C3_ER_IRQHandler 0000001A

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 511 in file startup_stm32h745xx_CM7.s
   Uses
      At line 151 in file startup_stm32h745xx_CM7.s
      At line 365 in file startup_stm32h745xx_CM7.s

I2C3_EV_IRQHandler 0000001A

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 510 in file startup_stm32h745xx_CM7.s
   Uses
      At line 150 in file startup_stm32h745xx_CM7.s
      At line 364 in file startup_stm32h745xx_CM7.s

I2C4_ER_IRQHandler 0000001A

Symbol: I2C4_ER_IRQHandler
   Definitions
      At line 533 in file startup_stm32h745xx_CM7.s
   Uses
      At line 174 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

      At line 387 in file startup_stm32h745xx_CM7.s

I2C4_EV_IRQHandler 0000001A

Symbol: I2C4_EV_IRQHandler
   Definitions
      At line 532 in file startup_stm32h745xx_CM7.s
   Uses
      At line 173 in file startup_stm32h745xx_CM7.s
      At line 386 in file startup_stm32h745xx_CM7.s

JPEG_IRQHandler 0000001A

Symbol: JPEG_IRQHandler
   Definitions
      At line 558 in file startup_stm32h745xx_CM7.s
   Uses
      At line 199 in file startup_stm32h745xx_CM7.s
      At line 412 in file startup_stm32h745xx_CM7.s

LPTIM1_IRQHandler 0000001A

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 530 in file startup_stm32h745xx_CM7.s
   Uses
      At line 171 in file startup_stm32h745xx_CM7.s
      At line 384 in file startup_stm32h745xx_CM7.s

LPTIM2_IRQHandler 0000001A

Symbol: LPTIM2_IRQHandler
   Definitions
      At line 574 in file startup_stm32h745xx_CM7.s
   Uses
      At line 216 in file startup_stm32h745xx_CM7.s
      At line 428 in file startup_stm32h745xx_CM7.s

LPTIM3_IRQHandler 0000001A

Symbol: LPTIM3_IRQHandler
   Definitions
      At line 575 in file startup_stm32h745xx_CM7.s
   Uses
      At line 217 in file startup_stm32h745xx_CM7.s
      At line 429 in file startup_stm32h745xx_CM7.s

LPTIM4_IRQHandler 0000001A

Symbol: LPTIM4_IRQHandler
   Definitions
      At line 576 in file startup_stm32h745xx_CM7.s
   Uses
      At line 218 in file startup_stm32h745xx_CM7.s
      At line 430 in file startup_stm32h745xx_CM7.s

LPTIM5_IRQHandler 0000001A

Symbol: LPTIM5_IRQHandler



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 577 in file startup_stm32h745xx_CM7.s
   Uses
      At line 219 in file startup_stm32h745xx_CM7.s
      At line 431 in file startup_stm32h745xx_CM7.s

LPUART1_IRQHandler 0000001A

Symbol: LPUART1_IRQHandler
   Definitions
      At line 578 in file startup_stm32h745xx_CM7.s
   Uses
      At line 220 in file startup_stm32h745xx_CM7.s
      At line 432 in file startup_stm32h745xx_CM7.s

LTDC_ER_IRQHandler 0000001A

Symbol: LTDC_ER_IRQHandler
   Definitions
      At line 526 in file startup_stm32h745xx_CM7.s
   Uses
      At line 167 in file startup_stm32h745xx_CM7.s
      At line 380 in file startup_stm32h745xx_CM7.s

LTDC_IRQHandler 0000001A

Symbol: LTDC_IRQHandler
   Definitions
      At line 525 in file startup_stm32h745xx_CM7.s
   Uses
      At line 166 in file startup_stm32h745xx_CM7.s
      At line 379 in file startup_stm32h745xx_CM7.s

MDIOS_IRQHandler 0000001A

Symbol: MDIOS_IRQHandler
   Definitions
      At line 557 in file startup_stm32h745xx_CM7.s
   Uses
      At line 198 in file startup_stm32h745xx_CM7.s
      At line 411 in file startup_stm32h745xx_CM7.s

MDIOS_WKUP_IRQHandler 0000001A

Symbol: MDIOS_WKUP_IRQHandler
   Definitions
      At line 556 in file startup_stm32h745xx_CM7.s
   Uses
      At line 197 in file startup_stm32h745xx_CM7.s
      At line 410 in file startup_stm32h745xx_CM7.s

MDMA_IRQHandler 0000001A

Symbol: MDMA_IRQHandler
   Definitions
      At line 559 in file startup_stm32h745xx_CM7.s
   Uses
      At line 200 in file startup_stm32h745xx_CM7.s
      At line 413 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols


MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 260 in file startup_stm32h745xx_CM7.s
   Uses
      At line 64 in file startup_stm32h745xx_CM7.s
      At line 261 in file startup_stm32h745xx_CM7.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 250 in file startup_stm32h745xx_CM7.s
   Uses
      At line 62 in file startup_stm32h745xx_CM7.s
      At line 251 in file startup_stm32h745xx_CM7.s

OTG_FS_EP1_IN_IRQHandler 0000001A

Symbol: OTG_FS_EP1_IN_IRQHandler
   Definitions
      At line 536 in file startup_stm32h745xx_CM7.s
   Uses
      At line 177 in file startup_stm32h745xx_CM7.s
      At line 390 in file startup_stm32h745xx_CM7.s

OTG_FS_EP1_OUT_IRQHandler 0000001A

Symbol: OTG_FS_EP1_OUT_IRQHandler
   Definitions
      At line 535 in file startup_stm32h745xx_CM7.s
   Uses
      At line 176 in file startup_stm32h745xx_CM7.s
      At line 389 in file startup_stm32h745xx_CM7.s

OTG_FS_IRQHandler 0000001A

Symbol: OTG_FS_IRQHandler
   Definitions
      At line 538 in file startup_stm32h745xx_CM7.s
   Uses
      At line 179 in file startup_stm32h745xx_CM7.s
      At line 392 in file startup_stm32h745xx_CM7.s

OTG_FS_WKUP_IRQHandler 0000001A

Symbol: OTG_FS_WKUP_IRQHandler
   Definitions
      At line 537 in file startup_stm32h745xx_CM7.s
   Uses
      At line 178 in file startup_stm32h745xx_CM7.s
      At line 391 in file startup_stm32h745xx_CM7.s

OTG_HS_EP1_IN_IRQHandler 0000001A

Symbol: OTG_HS_EP1_IN_IRQHandler
   Definitions



ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

      At line 513 in file startup_stm32h745xx_CM7.s
   Uses
      At line 153 in file startup_stm32h745xx_CM7.s
      At line 367 in file startup_stm32h745xx_CM7.s

OTG_HS_EP1_OUT_IRQHandler 0000001A

Symbol: OTG_HS_EP1_OUT_IRQHandler
   Definitions
      At line 512 in file startup_stm32h745xx_CM7.s
   Uses
      At line 152 in file startup_stm32h745xx_CM7.s
      At line 366 in file startup_stm32h745xx_CM7.s

OTG_HS_IRQHandler 0000001A

Symbol: OTG_HS_IRQHandler
   Definitions
      At line 515 in file startup_stm32h745xx_CM7.s
   Uses
      At line 155 in file startup_stm32h745xx_CM7.s
      At line 369 in file startup_stm32h745xx_CM7.s

OTG_HS_WKUP_IRQHandler 0000001A

Symbol: OTG_HS_WKUP_IRQHandler
   Definitions
      At line 514 in file startup_stm32h745xx_CM7.s
   Uses
      At line 154 in file startup_stm32h745xx_CM7.s
      At line 368 in file startup_stm32h745xx_CM7.s

PVD_AVD_IRQHandler 0000001A

Symbol: PVD_AVD_IRQHandler
   Definitions
      At line 442 in file startup_stm32h745xx_CM7.s
   Uses
      At line 79 in file startup_stm32h745xx_CM7.s
      At line 295 in file startup_stm32h745xx_CM7.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 283 in file startup_stm32h745xx_CM7.s
   Uses
      At line 74 in file startup_stm32h745xx_CM7.s
      At line 284 in file startup_stm32h745xx_CM7.s

QUADSPI_IRQHandler 0000001A

Symbol: QUADSPI_IRQHandler
   Definitions
      At line 529 in file startup_stm32h745xx_CM7.s
   Uses
      At line 170 in file startup_stm32h745xx_CM7.s
      At line 383 in file startup_stm32h745xx_CM7.s




ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 446 in file startup_stm32h745xx_CM7.s
   Uses
      At line 83 in file startup_stm32h745xx_CM7.s
      At line 299 in file startup_stm32h745xx_CM7.s

RNG_IRQHandler 0000001A

Symbol: RNG_IRQHandler
   Definitions
      At line 517 in file startup_stm32h745xx_CM7.s
   Uses
      At line 158 in file startup_stm32h745xx_CM7.s
      At line 371 in file startup_stm32h745xx_CM7.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 482 in file startup_stm32h745xx_CM7.s
   Uses
      At line 119 in file startup_stm32h745xx_CM7.s
      At line 336 in file startup_stm32h745xx_CM7.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 444 in file startup_stm32h745xx_CM7.s
   Uses
      At line 81 in file startup_stm32h745xx_CM7.s
      At line 297 in file startup_stm32h745xx_CM7.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 237 in file startup_stm32h745xx_CM7.s
   Uses
      At line 61 in file startup_stm32h745xx_CM7.s
      At line 238 in file startup_stm32h745xx_CM7.s

SAI1_IRQHandler 0000001A

Symbol: SAI1_IRQHandler
   Definitions
      At line 524 in file startup_stm32h745xx_CM7.s
   Uses
      At line 165 in file startup_stm32h745xx_CM7.s
      At line 378 in file startup_stm32h745xx_CM7.s

SAI2_IRQHandler 0000001A

Symbol: SAI2_IRQHandler
   Definitions
      At line 528 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 169 in file startup_stm32h745xx_CM7.s
      At line 382 in file startup_stm32h745xx_CM7.s

SAI3_IRQHandler 0000001A

Symbol: SAI3_IRQHandler
   Definitions
      At line 551 in file startup_stm32h745xx_CM7.s
   Uses
      At line 192 in file startup_stm32h745xx_CM7.s
      At line 405 in file startup_stm32h745xx_CM7.s

SAI4_IRQHandler 0000001A

Symbol: SAI4_IRQHandler
   Definitions
      At line 582 in file startup_stm32h745xx_CM7.s
   Uses
      At line 224 in file startup_stm32h745xx_CM7.s
      At line 436 in file startup_stm32h745xx_CM7.s

SDMMC1_IRQHandler 0000001A

Symbol: SDMMC1_IRQHandler
   Definitions
      At line 489 in file startup_stm32h745xx_CM7.s
   Uses
      At line 127 in file startup_stm32h745xx_CM7.s
      At line 343 in file startup_stm32h745xx_CM7.s

SDMMC2_IRQHandler 0000001A

Symbol: SDMMC2_IRQHandler
   Definitions
      At line 560 in file startup_stm32h745xx_CM7.s
   Uses
      At line 202 in file startup_stm32h745xx_CM7.s
      At line 414 in file startup_stm32h745xx_CM7.s

SPDIF_RX_IRQHandler 0000001A

Symbol: SPDIF_RX_IRQHandler
   Definitions
      At line 534 in file startup_stm32h745xx_CM7.s
   Uses
      At line 175 in file startup_stm32h745xx_CM7.s
      At line 388 in file startup_stm32h745xx_CM7.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 476 in file startup_stm32h745xx_CM7.s
   Uses
      At line 113 in file startup_stm32h745xx_CM7.s
      At line 330 in file startup_stm32h745xx_CM7.s

SPI2_IRQHandler 0000001A



ARM Macro Assembler    Page 19 Alphabetic symbol ordering
Relocatable symbols


Symbol: SPI2_IRQHandler
   Definitions
      At line 477 in file startup_stm32h745xx_CM7.s
   Uses
      At line 114 in file startup_stm32h745xx_CM7.s
      At line 331 in file startup_stm32h745xx_CM7.s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 491 in file startup_stm32h745xx_CM7.s
   Uses
      At line 129 in file startup_stm32h745xx_CM7.s
      At line 345 in file startup_stm32h745xx_CM7.s

SPI4_IRQHandler 0000001A

Symbol: SPI4_IRQHandler
   Definitions
      At line 521 in file startup_stm32h745xx_CM7.s
   Uses
      At line 162 in file startup_stm32h745xx_CM7.s
      At line 375 in file startup_stm32h745xx_CM7.s

SPI5_IRQHandler 0000001A

Symbol: SPI5_IRQHandler
   Definitions
      At line 522 in file startup_stm32h745xx_CM7.s
   Uses
      At line 163 in file startup_stm32h745xx_CM7.s
      At line 376 in file startup_stm32h745xx_CM7.s

SPI6_IRQHandler 0000001A

Symbol: SPI6_IRQHandler
   Definitions
      At line 523 in file startup_stm32h745xx_CM7.s
   Uses
      At line 164 in file startup_stm32h745xx_CM7.s
      At line 377 in file startup_stm32h745xx_CM7.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 274 in file startup_stm32h745xx_CM7.s
   Uses
      At line 71 in file startup_stm32h745xx_CM7.s
      At line 275 in file startup_stm32h745xx_CM7.s

SWPMI1_IRQHandler 0000001A

Symbol: SWPMI1_IRQHandler
   Definitions
      At line 552 in file startup_stm32h745xx_CM7.s
   Uses



ARM Macro Assembler    Page 20 Alphabetic symbol ordering
Relocatable symbols

      At line 193 in file startup_stm32h745xx_CM7.s
      At line 406 in file startup_stm32h745xx_CM7.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 287 in file startup_stm32h745xx_CM7.s
   Uses
      At line 75 in file startup_stm32h745xx_CM7.s
      At line 288 in file startup_stm32h745xx_CM7.s

TAMP_STAMP_IRQHandler 0000001A

Symbol: TAMP_STAMP_IRQHandler
   Definitions
      At line 443 in file startup_stm32h745xx_CM7.s
   Uses
      At line 80 in file startup_stm32h745xx_CM7.s
      At line 296 in file startup_stm32h745xx_CM7.s

TIM15_IRQHandler 0000001A

Symbol: TIM15_IRQHandler
   Definitions
      At line 553 in file startup_stm32h745xx_CM7.s
   Uses
      At line 194 in file startup_stm32h745xx_CM7.s
      At line 407 in file startup_stm32h745xx_CM7.s

TIM16_IRQHandler 0000001A

Symbol: TIM16_IRQHandler
   Definitions
      At line 554 in file startup_stm32h745xx_CM7.s
   Uses
      At line 195 in file startup_stm32h745xx_CM7.s
      At line 408 in file startup_stm32h745xx_CM7.s

TIM17_IRQHandler 0000001A

Symbol: TIM17_IRQHandler
   Definitions
      At line 555 in file startup_stm32h745xx_CM7.s
   Uses
      At line 196 in file startup_stm32h745xx_CM7.s
      At line 409 in file startup_stm32h745xx_CM7.s

TIM1_BRK_IRQHandler 0000001A

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 465 in file startup_stm32h745xx_CM7.s
   Uses
      At line 102 in file startup_stm32h745xx_CM7.s
      At line 319 in file startup_stm32h745xx_CM7.s

TIM1_CC_IRQHandler 0000001A




ARM Macro Assembler    Page 21 Alphabetic symbol ordering
Relocatable symbols

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 468 in file startup_stm32h745xx_CM7.s
   Uses
      At line 105 in file startup_stm32h745xx_CM7.s
      At line 322 in file startup_stm32h745xx_CM7.s

TIM1_TRG_COM_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 467 in file startup_stm32h745xx_CM7.s
   Uses
      At line 104 in file startup_stm32h745xx_CM7.s
      At line 321 in file startup_stm32h745xx_CM7.s

TIM1_UP_IRQHandler 0000001A

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 466 in file startup_stm32h745xx_CM7.s
   Uses
      At line 103 in file startup_stm32h745xx_CM7.s
      At line 320 in file startup_stm32h745xx_CM7.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 469 in file startup_stm32h745xx_CM7.s
   Uses
      At line 106 in file startup_stm32h745xx_CM7.s
      At line 323 in file startup_stm32h745xx_CM7.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 470 in file startup_stm32h745xx_CM7.s
   Uses
      At line 107 in file startup_stm32h745xx_CM7.s
      At line 324 in file startup_stm32h745xx_CM7.s

TIM4_IRQHandler 0000001A

Symbol: TIM4_IRQHandler
   Definitions
      At line 471 in file startup_stm32h745xx_CM7.s
   Uses
      At line 108 in file startup_stm32h745xx_CM7.s
      At line 325 in file startup_stm32h745xx_CM7.s

TIM5_IRQHandler 0000001A

Symbol: TIM5_IRQHandler
   Definitions
      At line 490 in file startup_stm32h745xx_CM7.s
   Uses
      At line 128 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 22 Alphabetic symbol ordering
Relocatable symbols

      At line 344 in file startup_stm32h745xx_CM7.s

TIM6_DAC_IRQHandler 0000001A

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 494 in file startup_stm32h745xx_CM7.s
   Uses
      At line 132 in file startup_stm32h745xx_CM7.s
      At line 348 in file startup_stm32h745xx_CM7.s

TIM7_IRQHandler 0000001A

Symbol: TIM7_IRQHandler
   Definitions
      At line 495 in file startup_stm32h745xx_CM7.s
   Uses
      At line 133 in file startup_stm32h745xx_CM7.s
      At line 349 in file startup_stm32h745xx_CM7.s

TIM8_BRK_TIM12_IRQHandler 0000001A

Symbol: TIM8_BRK_TIM12_IRQHandler
   Definitions
      At line 483 in file startup_stm32h745xx_CM7.s
   Uses
      At line 121 in file startup_stm32h745xx_CM7.s
      At line 337 in file startup_stm32h745xx_CM7.s

TIM8_CC_IRQHandler 0000001A

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 486 in file startup_stm32h745xx_CM7.s
   Uses
      At line 124 in file startup_stm32h745xx_CM7.s
      At line 340 in file startup_stm32h745xx_CM7.s

TIM8_TRG_COM_TIM14_IRQHandler 0000001A

Symbol: TIM8_TRG_COM_TIM14_IRQHandler
   Definitions
      At line 485 in file startup_stm32h745xx_CM7.s
   Uses
      At line 123 in file startup_stm32h745xx_CM7.s
      At line 339 in file startup_stm32h745xx_CM7.s

TIM8_UP_TIM13_IRQHandler 0000001A

Symbol: TIM8_UP_TIM13_IRQHandler
   Definitions
      At line 484 in file startup_stm32h745xx_CM7.s
   Uses
      At line 122 in file startup_stm32h745xx_CM7.s
      At line 338 in file startup_stm32h745xx_CM7.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler



ARM Macro Assembler    Page 23 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 492 in file startup_stm32h745xx_CM7.s
   Uses
      At line 130 in file startup_stm32h745xx_CM7.s
      At line 346 in file startup_stm32h745xx_CM7.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 493 in file startup_stm32h745xx_CM7.s
   Uses
      At line 131 in file startup_stm32h745xx_CM7.s
      At line 347 in file startup_stm32h745xx_CM7.s

UART7_IRQHandler 0000001A

Symbol: UART7_IRQHandler
   Definitions
      At line 519 in file startup_stm32h745xx_CM7.s
   Uses
      At line 160 in file startup_stm32h745xx_CM7.s
      At line 373 in file startup_stm32h745xx_CM7.s

UART8_IRQHandler 0000001A

Symbol: UART8_IRQHandler
   Definitions
      At line 520 in file startup_stm32h745xx_CM7.s
   Uses
      At line 161 in file startup_stm32h745xx_CM7.s
      At line 374 in file startup_stm32h745xx_CM7.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 478 in file startup_stm32h745xx_CM7.s
   Uses
      At line 115 in file startup_stm32h745xx_CM7.s
      At line 332 in file startup_stm32h745xx_CM7.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 479 in file startup_stm32h745xx_CM7.s
   Uses
      At line 116 in file startup_stm32h745xx_CM7.s
      At line 333 in file startup_stm32h745xx_CM7.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 480 in file startup_stm32h745xx_CM7.s
   Uses
      At line 117 in file startup_stm32h745xx_CM7.s
      At line 334 in file startup_stm32h745xx_CM7.s



ARM Macro Assembler    Page 24 Alphabetic symbol ordering
Relocatable symbols


USART6_IRQHandler 0000001A

Symbol: USART6_IRQHandler
   Definitions
      At line 509 in file startup_stm32h745xx_CM7.s
   Uses
      At line 149 in file startup_stm32h745xx_CM7.s
      At line 363 in file startup_stm32h745xx_CM7.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 270 in file startup_stm32h745xx_CM7.s
   Uses
      At line 66 in file startup_stm32h745xx_CM7.s
      At line 271 in file startup_stm32h745xx_CM7.s

WAKEUP_PIN_IRQHandler 0000001A

Symbol: WAKEUP_PIN_IRQHandler
   Definitions
      At line 584 in file startup_stm32h745xx_CM7.s
   Uses
      At line 227 in file startup_stm32h745xx_CM7.s
      At line 438 in file startup_stm32h745xx_CM7.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 441 in file startup_stm32h745xx_CM7.s
   Uses
      At line 78 in file startup_stm32h745xx_CM7.s
      At line 294 in file startup_stm32h745xx_CM7.s

WWDG_RST_IRQHandler 0000001A

Symbol: WWDG_RST_IRQHandler
   Definitions
      At line 579 in file startup_stm32h745xx_CM7.s
   Uses
      At line 221 in file startup_stm32h745xx_CM7.s
      At line 433 in file startup_stm32h745xx_CM7.s

__user_initial_stackheap 0000001C

Symbol: __user_initial_stackheap
   Definitions
      At line 606 in file startup_stm32h745xx_CM7.s
   Uses
      At line 604 in file startup_stm32h745xx_CM7.s
Comment: __user_initial_stackheap used once
157 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 43 in file startup_stm32h745xx_CM7.s
   Uses
      At line 47 in file startup_stm32h745xx_CM7.s
      At line 610 in file startup_stm32h745xx_CM7.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 32 in file startup_stm32h745xx_CM7.s
   Uses
      At line 35 in file startup_stm32h745xx_CM7.s
      At line 609 in file startup_stm32h745xx_CM7.s

__Vectors_Size 00000298

Symbol: __Vectors_Size
   Definitions
      At line 232 in file startup_stm32h745xx_CM7.s
   Uses
      At line 58 in file startup_stm32h745xx_CM7.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 239 in file startup_stm32h745xx_CM7.s
   Uses
      At line 242 in file startup_stm32h745xx_CM7.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 240 in file startup_stm32h745xx_CM7.s
   Uses
      At line 244 in file startup_stm32h745xx_CM7.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 603 in file startup_stm32h745xx_CM7.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
512 symbols in table
