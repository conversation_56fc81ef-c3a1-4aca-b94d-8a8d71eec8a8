/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : lcd_test.c
  * @brief          : LCD测试示例代码
  ******************************************************************************
  * @attention
  *
  * 这是一个LCD驱动的测试示例，展示如何使用LCD基础功能
  * 注意：此文件中的函数需要根据实际需求进行实现
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#include "lcd.h"
#include <stdio.h>

/* 函数声明 */
void lcd_draw_test(void);  // LCD绘图功能测试
void lcd_text_test(void);  // LCD字符显示测试
void lcd_comprehensive_test(void);  // LCD综合测试

/**
 * @brief       LCD测试函数示例
 * @param       无
 * @retval      无
 * @note        这是一个使用示例，展示LCD驱动的基本用法
 */
void lcd_test_example(void)
{
    printf("LCD测试开始...\r\n");
    
    /* 1. 初始化LCD */
    lcd_init();
    printf("LCD初始化完成，ID: 0x%04X\r\n", lcddev.id);

    /* 2. 开启LCD显示 */
    lcd_display_on();
    printf("LCD显示已开启\r\n");

    /* 3. 设置显示方向为竖屏 */
    lcd_display_dir(0);  // 0=竖屏, 1=横屏
    printf("显示方向设置为竖屏\r\n");
    
    /* 4. 设置扫描方向 */
    lcd_scan_dir(L2R_U2D);  // 从左到右，从上到下
    printf("扫描方向设置完成\r\n");

    /* 5. 清屏为白色 */
    // lcd_clear(WHITE);    // 需要用户实现

    /* 6. 设置画笔颜色 */
    g_point_color = RED;    // 红色画笔
    g_back_color = WHITE;   // 白色背景

    /* 7. 测试基本绘图功能 */
    lcd_draw_point(100, 100, RED);       // 画红色点
    lcd_draw_point(150, 150, GREEN);     // 画绿色点
    lcd_draw_point(200, 200, BLUE);      // 画蓝色点
    printf("画点测试完成\r\n");

    lcd_draw_line(0, 0, 100, 100, RED);      // 画红色线
    lcd_draw_rectangle(50, 50, 150, 100, GREEN); // 画绿色矩形

    /* 7. 测试窗口设置 */
    lcd_set_window(0, 0, 240, 320);     // 设置全屏窗口
    printf("窗口设置完成\r\n");

    /* 8. 测试光标设置 */
    lcd_set_cursor(120, 160);           // 设置光标到屏幕中心
    printf("光标设置完成\r\n");
    
    /* 9. 测试读点功能 */
    uint32_t color = lcd_read_point(100, 100);
    printf("坐标(100,100)的颜色值: 0x%08X\r\n", (unsigned int)color);
    
    /* 10. 测试不同扫描方向 */
    printf("测试不同扫描方向...\r\n");
    for(int i = 0; i < 8; i++)
    {
        lcd_scan_dir(i);
        printf("扫描方向 %d: 宽度=%d, 高度=%d\r\n", i, lcddev.width, lcddev.height);
        delay_ms(500);  // 延时500ms
    }
    
    /* 恢复默认扫描方向 */
    lcd_scan_dir(DFT_SCAN_DIR);
    
    printf("LCD测试完成！\r\n");
}

/**
 * @brief       LCD信息显示函数
 * @param       无
 * @retval      无
 */
void lcd_show_info(void)
{
    printf("\r\n=== LCD驱动信息 ===\r\n");
    printf("LCD ID: 0x%04X\r\n", lcddev.id);
    printf("LCD宽度: %d\r\n", lcddev.width);
    printf("LCD高度: %d\r\n", lcddev.height);
    printf("显示方向: %s\r\n", lcddev.dir ? "横屏" : "竖屏");
    printf("写GRAM命令: 0x%04X\r\n", lcddev.wramcmd);
    printf("设置X坐标命令: 0x%04X\r\n", lcddev.setxcmd);
    printf("设置Y坐标命令: 0x%04X\r\n", lcddev.setycmd);
    printf("画笔颜色: 0x%08X\r\n", (unsigned int)g_point_color);
    printf("背景颜色: 0x%08X\r\n", (unsigned int)g_back_color);
    
    /* 显示支持的LCD类型 */
    printf("\r\n=== 支持的LCD类型 ===\r\n");
    switch(lcddev.id)
    {
        case 0x9341:
            printf("检测到: ILI9341 LCD控制器\r\n");
            break;
        case 0x7789:
            printf("检测到: ST7789 LCD控制器\r\n");
            break;
        case 0x5310:
            printf("检测到: NT35310 LCD控制器\r\n");
            break;
        case 0x7796:
            printf("检测到: ST7796 LCD控制器\r\n");
            break;
        case 0x5510:
            printf("检测到: NT35510 LCD控制器\r\n");
            break;
        case 0x9806:
            printf("检测到: ILI9806 LCD控制器\r\n");
            break;
        case 0x1963:
            printf("检测到: SSD1963 LCD控制器\r\n");
            break;
        default:
            printf("未知的LCD控制器: 0x%04X\r\n", lcddev.id);
            break;
    }
    printf("==================\r\n\r\n");
}

/**
 * @brief       LCD扫描方向测试
 * @param       无
 * @retval      无
 */
void lcd_scan_dir_test(void)
{
    printf("=== LCD扫描方向测试 ===\r\n");
    
    const char* dir_names[] = {
        "L2R_U2D (从左到右,从上到下)",
        "L2R_D2U (从左到右,从下到上)", 
        "R2L_U2D (从右到左,从上到下)",
        "R2L_D2U (从右到左,从下到上)",
        "U2D_L2R (从上到下,从左到右)",
        "U2D_R2L (从上到下,从右到左)",
        "D2U_L2R (从下到上,从左到右)",
        "D2U_R2L (从下到上,从右到左)"
    };
    
    for(int i = 0; i < 8; i++)
    {
        printf("设置扫描方向 %d: %s\r\n", i, dir_names[i]);
        lcd_scan_dir(i);
        printf("  -> 当前分辨率: %d x %d\r\n", lcddev.width, lcddev.height);
        delay_ms(1000);  // 延时1秒观察效果
    }
    
    /* 恢复默认方向 */
    lcd_scan_dir(DFT_SCAN_DIR);
    printf("恢复默认扫描方向: %d\r\n", DFT_SCAN_DIR);
    printf("======================\r\n");
}

/**
 * @brief       LCD基础功能测试
 * @param       无  
 * @retval      无
 */
void lcd_basic_test(void)
{
    printf("=== LCD基础功能测试 ===\r\n");
    
    /* 测试窗口设置 */
    printf("测试窗口设置...\r\n");
    lcd_set_window(0, 0, lcddev.width, lcddev.height);
    
    /* 测试光标设置 */
    printf("测试光标设置...\r\n");
    lcd_set_cursor(lcddev.width/2, lcddev.height/2);
    
    /* 测试读点功能 */
    printf("测试读点功能...\r\n");
    for(int i = 0; i < 5; i++)
    {
        uint16_t x = i * 50;
        uint16_t y = i * 50;
        if(x < lcddev.width && y < lcddev.height)
        {
            uint32_t color = lcd_read_point(x, y);
            printf("坐标(%d,%d)颜色: 0x%08X\r\n", x, y, (unsigned int)color);
        }
    }
    
    printf("======================\r\n");
}

/**
 * @brief       LCD显示开关测试
 * @param       无
 * @retval      无
 */
void lcd_display_switch_test(void)
{
    printf("=== LCD显示开关测试 ===\r\n");

    printf("开启LCD显示...\r\n");
    lcd_display_on();
    delay_ms(2000);  // 延时2秒

    printf("关闭LCD显示...\r\n");
    lcd_display_off();
    delay_ms(2000);  // 延时2秒

    printf("重新开启LCD显示...\r\n");
    lcd_display_on();
    delay_ms(1000);  // 延时1秒

    printf("显示开关测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD光标设置测试
 * @param       无
 * @retval      无
 */
void lcd_cursor_test(void)
{
    printf("=== LCD光标设置测试 ===\r\n");

    /* 测试不同位置的光标设置 */
    uint16_t test_positions[][2] = {
        {0, 0},                                    // 左上角
        {lcddev.width-1, 0},                      // 右上角
        {0, lcddev.height-1},                     // 左下角
        {lcddev.width-1, lcddev.height-1},        // 右下角
        {lcddev.width/2, lcddev.height/2},        // 中心
    };

    const char* position_names[] = {
        "左上角", "右上角", "左下角", "右下角", "中心"
    };

    for(int i = 0; i < 5; i++)
    {
        uint16_t x = test_positions[i][0];
        uint16_t y = test_positions[i][1];

        printf("设置光标到%s: (%d, %d)\r\n", position_names[i], x, y);
        lcd_set_cursor(x, y);
        delay_ms(500);  // 延时500ms
    }

    printf("光标测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD显示方向测试
 * @param       无
 * @retval      无
 */
void lcd_display_dir_test(void)
{
    printf("=== LCD显示方向测试 ===\r\n");

    printf("当前显示方向: %s\r\n", lcddev.dir ? "横屏" : "竖屏");
    printf("当前分辨率: %d x %d\r\n", lcddev.width, lcddev.height);

    printf("切换到横屏模式...\r\n");
    lcd_display_dir(1);  // 横屏
    printf("横屏分辨率: %d x %d\r\n", lcddev.width, lcddev.height);
    delay_ms(2000);

    printf("切换到竖屏模式...\r\n");
    lcd_display_dir(0);  // 竖屏
    printf("竖屏分辨率: %d x %d\r\n", lcddev.width, lcddev.height);
    delay_ms(2000);

    printf("显示方向测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD画点功能测试
 * @param       无
 * @retval      无
 */
void lcd_draw_point_test(void)
{
    printf("=== LCD画点功能测试 ===\r\n");

    /* 测试不同颜色的点 */
    uint32_t colors[] = {RED, GREEN, BLUE, YELLOW, CYAN, MAGENTA, WHITE, BLACK};
    const char* color_names[] = {"红色", "绿色", "蓝色", "黄色", "青色", "品红", "白色", "黑色"};

    for(int i = 0; i < 8; i++)
    {
        uint16_t x = 50 + i * 30;
        uint16_t y = 50 + i * 20;

        printf("画%s点: (%d, %d)\r\n", color_names[i], x, y);
        lcd_draw_point(x, y, colors[i]);
        delay_ms(300);
    }

    /* 画一个简单的图案 */
    printf("画十字图案...\r\n");
    for(int i = 0; i < 20; i++)
    {
        lcd_draw_point(100 + i, 100, RED);      // 水平线
        lcd_draw_point(110, 90 + i, RED);       // 垂直线
    }

    printf("画点测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD绘图功能测试
 * @param       无
 * @retval      无
 */
void lcd_draw_test(void)
{
    printf("=== LCD绘图功能测试 ===\r\n");

    /* 清屏为白色 */
    lcd_clear(WHITE);
    delay_ms(500);

    /* 测试画线 */
    printf("测试画线功能...\r\n");
    lcd_draw_line(10, 10, 100, 100, RED);       // 红色对角线
    lcd_draw_line(100, 10, 10, 100, GREEN);     // 绿色对角线
    lcd_draw_hline(10, 120, 100, BLUE);         // 蓝色水平线
    delay_ms(1000);

    /* 测试画矩形 */
    printf("测试画矩形功能...\r\n");
    lcd_draw_rectangle(120, 10, 200, 80, MAGENTA);  // 品红色矩形
    lcd_draw_rectangle(130, 20, 190, 70, CYAN);     // 青色矩形
    delay_ms(1000);

    /* 测试画圆 */
    printf("测试画圆功能...\r\n");
    lcd_draw_circle(60, 180, 30, YELLOW);       // 黄色圆
    lcd_draw_circle(160, 180, 25, RED);         // 红色圆
    delay_ms(1000);

    /* 测试填充功能 */
    printf("测试填充功能...\r\n");
    lcd_fill(10, 220, 80, 280, BLUE);           // 蓝色填充矩形
    delay_ms(500);

    /* 测试颜色填充 */
    printf("测试颜色填充功能...\r\n");
    uint16_t color_array[20*20];
    for(int i = 0; i < 400; i++)
    {
        color_array[i] = (i % 2) ? RED : GREEN;  // 红绿相间
    }
    lcd_color_fill(120, 220, 139, 239, color_array);
    delay_ms(1000);

    printf("绘图测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD字符显示测试
 * @param       无
 * @retval      无
 */
void lcd_text_test(void)
{
    printf("=== LCD字符显示测试 ===\r\n");

    /* 清屏为白色 */
    lcd_clear(WHITE);
    delay_ms(500);

    /* 测试字符显示 */
    printf("测试字符显示...\r\n");
    lcd_show_char(10, 10, 'A', 16, 0, RED);     // 显示字符A
    lcd_show_char(30, 10, 'B', 16, 0, GREEN);   // 显示字符B
    lcd_show_char(50, 10, 'C', 16, 0, BLUE);    // 显示字符C
    delay_ms(1000);

    /* 测试数字显示 */
    printf("测试数字显示...\r\n");
    lcd_show_num(10, 40, 12345, 5, 16, MAGENTA);    // 显示数字12345
    lcd_show_xnum(10, 70, 678, 5, 16, 0x80, CYAN); // 显示数字00678
    delay_ms(1000);

    /* 测试字符串显示 */
    printf("测试字符串显示...\r\n");
    lcd_show_string(10, 100, 200, 24, 16, "Hello STM32H745!", YELLOW);
    lcd_show_string(10, 130, 200, 24, 16, "LCD Driver Test", RED);
    delay_ms(1000);

    /* 测试不同字体大小 */
    printf("测试不同字体大小...\r\n");
    lcd_show_string(10, 160, 200, 12, 12, "Size 12", GREEN);
    lcd_show_string(10, 180, 200, 16, 16, "Size 16", BLUE);
    lcd_show_string(10, 210, 200, 24, 24, "Size 24", MAGENTA);
    delay_ms(2000);

    printf("字符显示测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD综合测试函数
 * @param       无
 * @retval      无
 */
void lcd_comprehensive_test(void)
{
    printf("=== LCD综合功能测试开始 ===\r\n");

    /* 1. LCD初始化测试 */
    printf("1. LCD初始化测试...\r\n");
    lcd_init();
    lcd_display_on();
    printf("LCD初始化完成\r\n");

    /* 2. 基础绘图测试 */
    printf("2. 基础绘图测试...\r\n");
    lcd_draw_test();
    delay_ms(2000);

    /* 3. 字符显示测试 */
    printf("3. 字符显示测试...\r\n");
    lcd_text_test();
    delay_ms(2000);

    /* 4. 综合演示 */
    printf("4. 综合演示...\r\n");
    lcd_clear(BLACK);

    /* 绘制边框 */
    lcd_draw_rectangle(5, 5, lcddev.width-6, lcddev.height-6, WHITE);

    /* 显示标题 */
    lcd_show_string(20, 20, 200, 16, 16, "STM32H745 LCD Test", YELLOW);

    /* 显示系统信息 */
    lcd_show_string(20, 50, 200, 16, 16, "Resolution:", GREEN);
    lcd_show_num(120, 50, lcddev.width, 3, 16, WHITE);
    lcd_show_char(150, 50, 'x', 16, 0, WHITE);
    lcd_show_num(170, 50, lcddev.height, 3, 16, WHITE);

    /* 显示LCD ID */
    lcd_show_string(20, 80, 200, 16, 16, "LCD ID: 0x", CYAN);
    lcd_show_xnum(120, 80, lcddev.id, 4, 16, 0x80, WHITE);

    /* 绘制彩色圆形 */
    lcd_fill_circle(60, 150, 25, RED);
    lcd_fill_circle(120, 150, 25, GREEN);
    lcd_fill_circle(180, 150, 25, BLUE);

    /* 显示完成信息 */
    lcd_show_string(20, 200, 200, 16, 16, "Test Complete!", MAGENTA);

    printf("LCD综合测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       SSD1963背光测试
 * @param       无
 * @retval      无
 */
void lcd_backlight_test(void)
{
    printf("=== SSD1963背光测试 ===\r\n");

    if(lcddev.id != 0x1963)
    {
        printf("当前LCD不是SSD1963，跳过背光测试\r\n");
        printf("======================\r\n");
        return;
    }

    printf("SSD1963背光亮度测试...\r\n");

    /* 测试不同亮度级别 */
    uint8_t brightness_levels[] = {0, 25, 50, 75, 100};

    for(int i = 0; i < 5; i++)
    {
        printf("设置背光亮度: %d%%\r\n", brightness_levels[i]);
        lcd_ssd_backlight_set(brightness_levels[i]);
        delay_ms(1500);  // 延时1.5秒观察效果
    }

    /* 恢复到80%亮度 */
    printf("恢复背光亮度到80%%\r\n");
    lcd_ssd_backlight_set(80);

    printf("背光测试完成\r\n");
    printf("======================\r\n");
}

/**
 * @brief       LCD窗口设置测试
 * @param       无
 * @retval      无
 */
void lcd_window_test(void)
{
    printf("=== LCD窗口设置测试 ===\r\n");

    /* 测试不同大小的窗口 */
    struct {
        uint16_t x, y, w, h;
        const char* name;
    } windows[] = {
        {0, 0, lcddev.width, lcddev.height, "全屏窗口"},
        {50, 50, 100, 100, "中等窗口"},
        {10, 10, 50, 50, "小窗口"},
        {lcddev.width/4, lcddev.height/4, lcddev.width/2, lcddev.height/2, "居中窗口"}
    };

    for(int i = 0; i < 4; i++)
    {
        printf("设置%s: (%d,%d) 大小:%dx%d\r\n",
               windows[i].name, windows[i].x, windows[i].y,
               windows[i].w, windows[i].h);

        lcd_set_window(windows[i].x, windows[i].y, windows[i].w, windows[i].h);
        delay_ms(1000);
    }

    /* 恢复全屏窗口 */
    lcd_set_window(0, 0, lcddev.width, lcddev.height);
    printf("恢复全屏窗口\r\n");

    printf("窗口测试完成\r\n");
    printf("======================\r\n");
}

/* 注意：以下函数需要用户根据实际需求实现 */

/*
// 延时函数示例实现
void delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

// 清屏函数示例实现  
void lcd_clear(uint32_t color)
{
    lcd_set_window(0, 0, lcddev.width, lcddev.height);
    lcd_write_ram_prepare();
    
    for(uint32_t i = 0; i < lcddev.width * lcddev.height; i++)
    {
        lcd_wr_data(color);
    }
}

// 画点函数示例实现
void lcd_draw_point(uint16_t x, uint16_t y)
{
    lcd_set_cursor(x, y);
    lcd_write_ram_prepare();
    lcd_wr_data(g_point_color);
}
*/
