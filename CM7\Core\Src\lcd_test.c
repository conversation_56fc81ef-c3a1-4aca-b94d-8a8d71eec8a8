/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : lcd_test.c
  * @brief          : LCD测试示例代码
  ******************************************************************************
  * @attention
  *
  * 这是一个LCD驱动的测试示例，展示如何使用LCD基础功能
  * 注意：此文件中的函数需要根据实际需求进行实现
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#include "lcd.h"
#include <stdio.h>

/**
 * @brief       LCD测试函数示例
 * @param       无
 * @retval      无
 * @note        这是一个使用示例，展示LCD驱动的基本用法
 */
void lcd_test_example(void)
{
    printf("LCD测试开始...\r\n");
    
    /* 1. 初始化LCD */
    lcd_init();
    printf("LCD初始化完成，ID: 0x%04X\r\n", lcddev.id);
    
    /* 2. 设置显示方向为竖屏 */
    // lcd_display_dir(0);  // 需要用户实现
    
    /* 3. 设置扫描方向 */
    lcd_scan_dir(L2R_U2D);  // 从左到右，从上到下
    
    /* 4. 清屏为白色 */
    // lcd_clear(WHITE);    // 需要用户实现
    
    /* 5. 设置画笔颜色 */
    g_point_color = RED;    // 红色画笔
    g_back_color = WHITE;   // 白色背景
    
    /* 6. 测试基本绘图功能 */
    // lcd_draw_point(100, 100);           // 画点 - 需要用户实现
    // lcd_draw_line(0, 0, 100, 100);      // 画线 - 需要用户实现
    // lcd_draw_rectangle(50, 50, 150, 100); // 画矩形 - 需要用户实现
    
    /* 7. 测试窗口设置 */
    lcd_set_window(0, 0, 240, 320);     // 设置全屏窗口
    
    /* 8. 测试光标设置 */
    lcd_set_cursor(120, 160);           // 设置光标到屏幕中心
    
    /* 9. 测试读点功能 */
    uint32_t color = lcd_read_point(100, 100);
    printf("坐标(100,100)的颜色值: 0x%08X\r\n", (unsigned int)color);
    
    /* 10. 测试不同扫描方向 */
    printf("测试不同扫描方向...\r\n");
    for(int i = 0; i < 8; i++)
    {
        lcd_scan_dir(i);
        printf("扫描方向 %d: 宽度=%d, 高度=%d\r\n", i, lcddev.width, lcddev.height);
        delay_ms(500);  // 延时500ms
    }
    
    /* 恢复默认扫描方向 */
    lcd_scan_dir(DFT_SCAN_DIR);
    
    printf("LCD测试完成！\r\n");
}

/**
 * @brief       LCD信息显示函数
 * @param       无
 * @retval      无
 */
void lcd_show_info(void)
{
    printf("\r\n=== LCD驱动信息 ===\r\n");
    printf("LCD ID: 0x%04X\r\n", lcddev.id);
    printf("LCD宽度: %d\r\n", lcddev.width);
    printf("LCD高度: %d\r\n", lcddev.height);
    printf("显示方向: %s\r\n", lcddev.dir ? "横屏" : "竖屏");
    printf("写GRAM命令: 0x%04X\r\n", lcddev.wramcmd);
    printf("设置X坐标命令: 0x%04X\r\n", lcddev.setxcmd);
    printf("设置Y坐标命令: 0x%04X\r\n", lcddev.setycmd);
    printf("画笔颜色: 0x%08X\r\n", (unsigned int)g_point_color);
    printf("背景颜色: 0x%08X\r\n", (unsigned int)g_back_color);
    
    /* 显示支持的LCD类型 */
    printf("\r\n=== 支持的LCD类型 ===\r\n");
    switch(lcddev.id)
    {
        case 0x9341:
            printf("检测到: ILI9341 LCD控制器\r\n");
            break;
        case 0x7789:
            printf("检测到: ST7789 LCD控制器\r\n");
            break;
        case 0x5310:
            printf("检测到: NT35310 LCD控制器\r\n");
            break;
        case 0x7796:
            printf("检测到: ST7796 LCD控制器\r\n");
            break;
        case 0x5510:
            printf("检测到: NT35510 LCD控制器\r\n");
            break;
        case 0x9806:
            printf("检测到: ILI9806 LCD控制器\r\n");
            break;
        case 0x1963:
            printf("检测到: SSD1963 LCD控制器\r\n");
            break;
        default:
            printf("未知的LCD控制器: 0x%04X\r\n", lcddev.id);
            break;
    }
    printf("==================\r\n\r\n");
}

/**
 * @brief       LCD扫描方向测试
 * @param       无
 * @retval      无
 */
void lcd_scan_dir_test(void)
{
    printf("=== LCD扫描方向测试 ===\r\n");
    
    const char* dir_names[] = {
        "L2R_U2D (从左到右,从上到下)",
        "L2R_D2U (从左到右,从下到上)", 
        "R2L_U2D (从右到左,从上到下)",
        "R2L_D2U (从右到左,从下到上)",
        "U2D_L2R (从上到下,从左到右)",
        "U2D_R2L (从上到下,从右到左)",
        "D2U_L2R (从下到上,从左到右)",
        "D2U_R2L (从下到上,从右到左)"
    };
    
    for(int i = 0; i < 8; i++)
    {
        printf("设置扫描方向 %d: %s\r\n", i, dir_names[i]);
        lcd_scan_dir(i);
        printf("  -> 当前分辨率: %d x %d\r\n", lcddev.width, lcddev.height);
        delay_ms(1000);  // 延时1秒观察效果
    }
    
    /* 恢复默认方向 */
    lcd_scan_dir(DFT_SCAN_DIR);
    printf("恢复默认扫描方向: %d\r\n", DFT_SCAN_DIR);
    printf("======================\r\n");
}

/**
 * @brief       LCD基础功能测试
 * @param       无  
 * @retval      无
 */
void lcd_basic_test(void)
{
    printf("=== LCD基础功能测试 ===\r\n");
    
    /* 测试窗口设置 */
    printf("测试窗口设置...\r\n");
    lcd_set_window(0, 0, lcddev.width, lcddev.height);
    
    /* 测试光标设置 */
    printf("测试光标设置...\r\n");
    lcd_set_cursor(lcddev.width/2, lcddev.height/2);
    
    /* 测试读点功能 */
    printf("测试读点功能...\r\n");
    for(int i = 0; i < 5; i++)
    {
        uint16_t x = i * 50;
        uint16_t y = i * 50;
        if(x < lcddev.width && y < lcddev.height)
        {
            uint32_t color = lcd_read_point(x, y);
            printf("坐标(%d,%d)颜色: 0x%08X\r\n", x, y, (unsigned int)color);
        }
    }
    
    printf("======================\r\n");
}

/* 注意：以下函数需要用户根据实际需求实现 */

/*
// 延时函数示例实现
void delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

// 清屏函数示例实现  
void lcd_clear(uint32_t color)
{
    lcd_set_window(0, 0, lcddev.width, lcddev.height);
    lcd_write_ram_prepare();
    
    for(uint32_t i = 0; i < lcddev.width * lcddev.height; i++)
    {
        lcd_wr_data(color);
    }
}

// 画点函数示例实现
void lcd_draw_point(uint16_t x, uint16_t y)
{
    lcd_set_cursor(x, y);
    lcd_write_ram_prepare();
    lcd_wr_data(g_point_color);
}
*/
