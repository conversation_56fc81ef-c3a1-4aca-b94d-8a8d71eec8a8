@echo off
echo 正在编译双仪表盘程序...

REM 设置编译器路径
set GCC_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set PROGRAMMER_PATH=C:\ST\STM32CubeCLT_1.18.0\STM32CubeProgrammer\bin

REM 创建build目录
if not exist build mkdir build

REM 编译主程序
echo 编译main.c...
%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c Core/Src/main.c -o build/main.o

if %errorlevel% neq 0 (
    echo 编译main.c失败
    pause
    exit /b 1
)

REM 编译HAL库文件
echo 编译HAL库...
%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c -o build/stm32h7xx_hal.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c -o build/stm32h7xx_hal_cortex.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c -o build/stm32h7xx_hal_rcc.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c -o build/stm32h7xx_hal_rcc_ex.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c -o build/stm32h7xx_hal_pwr.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c -o build/stm32h7xx_hal_pwr_ex.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c -o build/stm32h7xx_hal_hsem.o

REM 编译系统文件
echo 编译系统文件...
%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c ../Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c -o build/system_stm32h7xx.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c Core/Src/stm32h7xx_it.c -o build/stm32h7xx_it.o

%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c Core/Src/stm32h7xx_hal_msp.c -o build/stm32h7xx_hal_msp.o

REM 编译启动文件
echo 编译启动文件...
%GCC_PATH%\arm-none-eabi-gcc.exe -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -c startup_stm32h745xx.s -o build/startup_stm32h745xx.o

REM 链接生成elf文件
echo 链接生成elf文件...
%GCC_PATH%\arm-none-eabi-gcc.exe build/main.o build/stm32h7xx_hal.o build/stm32h7xx_hal_cortex.o build/stm32h7xx_hal_rcc.o build/stm32h7xx_hal_rcc_ex.o build/stm32h7xx_hal_pwr.o build/stm32h7xx_hal_pwr_ex.o build/stm32h7xx_hal_hsem.o build/system_stm32h7xx.o build/stm32h7xx_it.o build/stm32h7xx_hal_msp.o build/startup_stm32h745xx.o -mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -specs=nano.specs -TSTM32H745XIHx_FLASH.ld -lc -lm -lnosys -Wl,-Map=build/gauge_demo.map,--cref -Wl,--gc-sections -o build/gauge_demo.elf

if %errorlevel% neq 0 (
    echo 链接失败
    pause
    exit /b 1
)

REM 生成hex文件
echo 生成hex文件...
%GCC_PATH%\arm-none-eabi-objcopy.exe -O ihex build/gauge_demo.elf build/gauge_demo.hex

if %errorlevel% neq 0 (
    echo 生成hex文件失败
    pause
    exit /b 1
)

echo 编译完成！生成文件：build/gauge_demo.hex

REM 下载到开发板
echo 正在下载到开发板...
%PROGRAMMER_PATH%\STM32_Programmer_CLI.exe -c port=SWD -w build/gauge_demo.hex -v -rst

if %errorlevel% neq 0 (
    echo 下载失败
    pause
    exit /b 1
)

echo 双仪表盘程序下载完成！
pause
