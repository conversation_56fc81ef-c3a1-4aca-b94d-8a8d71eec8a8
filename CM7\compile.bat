@echo off
echo === STM32H745 LCD项目编译脚本 ===

set GCC_PATH=C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin
set CC=%GCC_PATH%\arm-none-eabi-gcc.exe
set OBJCOPY=%GCC_PATH%\arm-none-eabi-objcopy.exe
set SIZE=%GCC_PATH%\arm-none-eabi-size.exe

echo 编译器路径: %CC%

REM 创建输出目录
if not exist "build" mkdir build

echo 正在编译源文件...

REM 编译参数
set CFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -DUSE_HAL_DRIVER -DSTM32H745xx -DCORE_CM7 -ICore/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc -I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32H7xx/Include -I../Drivers/CMSIS/Include -Og -Wall -fdata-sections -ffunction-sections -g -gdwarf-2 -MMD -MP

REM 链接参数
set LDFLAGS=-mcpu=cortex-m7 -mthumb -mfpu=fpv5-d16 -mfloat-abi=hard -specs=nano.specs -TSTM32H745XIHx_FLASH.ld -lc -lm -lnosys -Wl,-Map=build/LCD_CM7.map,--cref -Wl,--gc-sections

REM 编译所有C文件
%CC% %CFLAGS% -c Core/Src/main.c -o build/main.o
%CC% %CFLAGS% -c Core/Src/lcd.c -o build/lcd.o
%CC% %CFLAGS% -c Core/Src/font.c -o build/font.o
%CC% %CFLAGS% -c Core/Src/lcd_test.c -o build/lcd_test.o
%CC% %CFLAGS% -c Core/Src/stm32h7xx_it.c -o build/stm32h7xx_it.o
%CC% %CFLAGS% -c Core/Src/stm32h7xx_hal_msp.c -o build/stm32h7xx_hal_msp.o

REM 编译HAL库文件
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c -o build/stm32h7xx_hal_cortex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c -o build/stm32h7xx_hal_rcc.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c -o build/stm32h7xx_hal_rcc_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c -o build/stm32h7xx_hal_flash.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c -o build/stm32h7xx_hal_flash_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c -o build/stm32h7xx_hal_gpio.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c -o build/stm32h7xx_hal_hsem.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c -o build/stm32h7xx_hal_dma.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c -o build/stm32h7xx_hal_dma_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c -o build/stm32h7xx_hal_mdma.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c -o build/stm32h7xx_hal_pwr.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c -o build/stm32h7xx_hal_pwr_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c -o build/stm32h7xx_hal.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c -o build/stm32h7xx_hal_i2c.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c -o build/stm32h7xx_hal_i2c_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c -o build/stm32h7xx_hal_exti.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c -o build/stm32h7xx_hal_tim.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c -o build/stm32h7xx_hal_tim_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c -o build/stm32h7xx_hal_uart.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c -o build/stm32h7xx_hal_uart_ex.o
%CC% %CFLAGS% -c ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sram.c -o build/stm32h7xx_hal_sram.o

REM 编译CMSIS文件
%CC% %CFLAGS% -c ../Drivers/CMSIS/Device/ST/STM32H7xx/Source/Templates/system_stm32h7xx.c -o build/system_stm32h7xx.o

REM 编译启动文件
%CC% %CFLAGS% -c startup_stm32h745xx.s -o build/startup_stm32h745xx.o

echo 正在链接...

REM 链接所有目标文件
%CC% %LDFLAGS% build/*.o -o build/LCD_CM7.elf

if %ERRORLEVEL% EQU 0 (
    echo 链接成功！
    
    REM 生成hex和bin文件
    %OBJCOPY% -O ihex build/LCD_CM7.elf build/LCD_CM7.hex
    %OBJCOPY% -O binary build/LCD_CM7.elf build/LCD_CM7.bin
    
    REM 显示大小信息
    %SIZE% build/LCD_CM7.elf
    
    echo.
    echo === 编译完成 ===
    echo HEX文件: build/LCD_CM7.hex
    echo BIN文件: build/LCD_CM7.bin
    echo ELF文件: build/LCD_CM7.elf
) else (
    echo 编译失败！
)

pause
