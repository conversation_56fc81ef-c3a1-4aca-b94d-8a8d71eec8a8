<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [LCD_CM7\LCD_CM7.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image LCD_CM7\LCD_CM7.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Jul 04 16:27:28 2025
<BR><P>
<H3>Maximum Stack Usage =        224 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[d4]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[8d]">ADC3_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8d]">ADC3_IRQHandler</a><BR>
 <LI><a href="#[d]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[d]">BusFault_Handler</a><BR>
 <LI><a href="#[b]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">HardFault_Handler</a><BR>
 <LI><a href="#[c]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c]">MemManage_Handler</a><BR>
 <LI><a href="#[a]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">NMI_Handler</a><BR>
 <LI><a href="#[e]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[8d]">ADC3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[25]">ADC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8f]">BDMA_Channel0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[90]">BDMA_Channel1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[91]">BDMA_Channel2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[92]">BDMA_Channel3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[93]">BDMA_Channel4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[94]">BDMA_Channel5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[95]">BDMA_Channel6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[96]">BDMA_Channel7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[d]">BusFault_Handler</a> from stm32h7xx_it_1.o(i.BusFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6d]">CEC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[53]">CM4_SEV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[52]">CM7_SEV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[97]">COMP1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9e]">CRS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5e]">DCMI_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7d]">DFSDM1_FLT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7e]">DFSDM1_FLT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7f]">DFSDM1_FLT2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[80]">DFSDM1_FLT3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[20]">DMA1_Stream2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[21]">DMA1_Stream3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[22]">DMA1_Stream4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[23]">DMA1_Stream5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[24]">DMA1_Stream6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[41]">DMA1_Stream7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[69]">DMA2D_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[54]">DMA2_Stream5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[55]">DMA2_Stream6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[56]">DMA2_Stream7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[75]">DMAMUX1_OVR_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8e]">DMAMUX2_OVR_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[10]">DebugMon_Handler</a> from stm32h7xx_it_1.o(i.DebugMon_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9f]">ECC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4f]">ETH_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[50]">ETH_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[19]">EXTI0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3b]">EXTI15_10_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1a]">EXTI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1b]">EXTI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1c]">EXTI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1d]">EXTI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2a]">EXTI9_5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[26]">FDCAN1_IT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[28]">FDCAN1_IT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[27]">FDCAN2_IT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[29]">FDCAN2_IT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[51]">FDCAN_CAL_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[17]">FLASH_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[42]">FMC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[60]">FPU_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a1]">HOLD_CORE_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7c]">HRTIM1_FLT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[76]">HRTIM1_Master_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[77]">HRTIM1_TIMA_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[78]">HRTIM1_TIMB_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[79]">HRTIM1_TIMC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7a]">HRTIM1_TIMD_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7b]">HRTIM1_TIME_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8b]">HSEM1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8c]">HSEM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[b]">HardFault_Handler</a> from stm32h7xx_it_1.o(i.HardFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[33]">I2C1_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[32]">I2C1_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[35]">I2C2_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[34]">I2C2_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[59]">I2C3_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[58]">I2C3_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6f]">I2C4_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6e]">I2C4_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[88]">JPEG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6c]">LPTIM1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[98]">LPTIM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[99]">LPTIM3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9a]">LPTIM4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9b]">LPTIM5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9c]">LPUART1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[68]">LTDC_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[67]">LTDC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[87]">MDIOS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[86]">MDIOS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[89]">MDMA_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[c]">MemManage_Handler</a> from stm32h7xx_it_1.o(i.MemManage_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a]">NMI_Handler</a> from stm32h7xx_it_1.o(i.NMI_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[72]">OTG_FS_EP1_IN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[71]">OTG_FS_EP1_OUT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[74]">OTG_FS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[73]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5b]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5a]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5d]">OTG_HS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5c]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[14]">PVD_AVD_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[11]">PendSV_Handler</a> from stm32h7xx_it_1.o(i.PendSV_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6b]">QUADSPI_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[18]">RCC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5f]">RNG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3c]">RTC_Alarm_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[16]">RTC_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9]">Reset_Handler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[66]">SAI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6a]">SAI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[81]">SAI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a0]">SAI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[43]">SDMMC1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8a]">SDMMC2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[70]">SPDIF_RX_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[36]">SPI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[37]">SPI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[45]">SPI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[63]">SPI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[64]">SPI5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[65]">SPI6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[f]">SVC_Handler</a> from stm32h7xx_it_1.o(i.SVC_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[82]">SWPMI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[12]">SysTick_Handler</a> from stm32h7xx_it_1.o(i.SysTick_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a3]">SystemInit</a> from system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit) referenced from startup_stm32h745xx_cm7.o(.text)
 <LI><a href="#[15]">TAMP_STAMP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[83]">TIM15_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[84]">TIM16_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[85]">TIM17_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2b]">TIM1_BRK_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2e]">TIM1_CC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2d]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2c]">TIM1_UP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2f]">TIM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[30]">TIM3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[31]">TIM4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[44]">TIM5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[48]">TIM6_DAC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[49]">TIM7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3d]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[40]">TIM8_CC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3f]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3e]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[46]">UART4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[47]">UART5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[61]">UART7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[62]">UART8_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[38]">USART1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[39]">USART2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3a]">USART3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[57]">USART6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[e]">UsageFault_Handler</a> from stm32h7xx_it_1.o(i.UsageFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a2]">WAKEUP_PIN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[13]">WWDG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9d]">WWDG_RST_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a7]">__main</a> from __main.o(!!!main) referenced from startup_stm32h745xx_cm7.o(.text)
 <LI><a href="#[a6]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[a5]">fputc</a> from fputc.o(i.fputc) referenced from _printf_char_file.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[a7]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[a8]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[aa]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[113]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[114]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[ab]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[115]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[ac]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[c4]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[116]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[b9]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[ae]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[b0]"></a>__rt_lib_init_heap_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_heap_2 &rArr; _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[117]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[118]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[119]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[11a]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[11b]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[11c]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[11d]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[11e]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[11f]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[120]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[121]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[122]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[123]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[b2]"></a>__rt_lib_init_stdio_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000024))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_stdio_2 &rArr; _initio &rArr; freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[124]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[125]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[126]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[127]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[128]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[129]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[12a]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[be]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[12b]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[12c]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[b4]"></a>__rt_lib_shutdown_stdio_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = __rt_lib_shutdown_stdio_2 &rArr; _terminateio &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
</UL>

<P><STRONG><a name="[12d]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[12e]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[12f]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[130]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[131]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[132]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[a9]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[133]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[b6]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[b8]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[134]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[ba]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 224 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[135]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[e1]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[bd]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[136]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[bf]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[9]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[137]"></a>_maybe_terminate_alloc</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, maybetermalloc1.o(.emb_text), UNUSED)

<P><STRONG><a name="[8d]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>BDMA_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>BDMA_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>BDMA_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>BDMA_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>BDMA_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>BDMA_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[95]"></a>BDMA_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[96]"></a>BDMA_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>CM4_SEV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CM7_SEV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[97]"></a>COMP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9e]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>DFSDM1_FLT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>DFSDM1_FLT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>DFSDM1_FLT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>DFSDM1_FLT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>DMAMUX1_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>DMAMUX2_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9f]"></a>ECC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>FDCAN1_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>FDCAN1_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>FDCAN2_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>FDCAN2_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>FDCAN_CAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[a1]"></a>HOLD_CORE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>HRTIM1_FLT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>HRTIM1_Master_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>HRTIM1_TIMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>HRTIM1_TIMB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>HRTIM1_TIMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>HRTIM1_TIMD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>HRTIM1_TIME_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>HSEM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>HSEM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>I2C4_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>I2C4_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>JPEG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[98]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>LPTIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9a]"></a>LPTIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9b]"></a>LPTIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9c]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>MDIOS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>MDIOS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>MDMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>OTG_FS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>OTG_FS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>PVD_AVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>QUADSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>SAI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>SAI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[a0]"></a>SAI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>SDMMC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>SDMMC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>SPDIF_RX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>SWPMI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[a2]"></a>WAKEUP_PIN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9d]"></a>WWDG_RST_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[d4]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32h745xx_cm7.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[c1]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>__printf</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, __printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[ad]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[cb]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
</UL>

<P><STRONG><a name="[138]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[139]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[13a]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[13b]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[5]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[c6]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[c8]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[c5]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[c2]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[c7]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ftell_internal
</UL>

<P><STRONG><a name="[13c]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[13d]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[c9]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[ca]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[b3]"></a>_initio</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, initio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = _initio &rArr; freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setvbuf
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_stdio_2
</UL>

<P><STRONG><a name="[b5]"></a>_terminateio</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, initio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = _terminateio &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown_stdio_2
</UL>

<P><STRONG><a name="[d1]"></a>_sys_open</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _sys_open &rArr; strlen
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[e0]"></a>_sys_close</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_close
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>

<P><STRONG><a name="[eb]"></a>_sys_write</STRONG> (Thumb, 16 bytes, Stack size 24 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _sys_write
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>

<P><STRONG><a name="[13e]"></a>_sys_read</STRONG> (Thumb, 14 bytes, Stack size 24 bytes, sys_io.o(.text), UNUSED)

<P><STRONG><a name="[da]"></a>_sys_istty</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_istty
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[ea]"></a>_sys_seek</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _sys_seek
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>

<P><STRONG><a name="[13f]"></a>_sys_ensure</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys_io.o(.text), UNUSED)

<P><STRONG><a name="[d9]"></a>_sys_flen</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys_io.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _sys_flen
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[140]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[141]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[b7]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[d0]"></a>free</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, h1_free.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = free
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>

<P><STRONG><a name="[d6]"></a>__flsbuf</STRONG> (Thumb, 470 bytes, Stack size 32 bytes, flsbuf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_deferredlazyseek
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_seterr
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_flen
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_istty
</UL>

<P><STRONG><a name="[10b]"></a>__flsbuf_byte</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, flsbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __flsbuf_byte
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[142]"></a>__flsbuf_wide</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, flsbuf.o(.text), UNUSED)

<P><STRONG><a name="[ce]"></a>setvbuf</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, setvbuf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = setvbuf
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[cc]"></a>freopen</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, fopen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = freopen &rArr; _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_open
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
</UL>

<P><STRONG><a name="[de]"></a>fopen</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, fopen.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[cf]"></a>_fclose_internal</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, fclose.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _fclose_internal &rArr; _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_close
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_terminateio
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[143]"></a>fclose</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, fclose.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[cd]"></a>__rt_SIGRTRED</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtred_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTRED &rArr; __rt_SIGRTRED_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED_inner
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_initio
</UL>

<P><STRONG><a name="[144]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[145]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[146]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[d5]"></a>__rt_heap_descriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_heap_descriptor_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>

<P><STRONG><a name="[147]"></a>__use_no_heap</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[148]"></a>__heap$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>_terminate_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>_init_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[e4]"></a>__Heap_Full</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[e6]"></a>__Heap_Broken</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[b1]"></a>_init_alloc</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Initialize
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_heap_2
</UL>

<P><STRONG><a name="[db]"></a>malloc</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, h1_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fopen
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[dd]"></a>_fseek</STRONG> (Thumb, 242 bytes, Stack size 24 bytes, fseek.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _fseek &rArr; _ftell_internal
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ftell_internal
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_seterr
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_flen
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_istty
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freopen
</UL>

<P><STRONG><a name="[149]"></a>fseek</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, fseek.o(.text), UNUSED)

<P><STRONG><a name="[d8]"></a>_seterr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stdio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[dc]"></a>_writebuf</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, stdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_seterr
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_seek
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_write
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[df]"></a>_fflush</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stdio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _fflush &rArr; _writebuf &rArr; _sys_write
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_writebuf
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_deferredlazyseek
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fclose_internal
</UL>

<P><STRONG><a name="[d7]"></a>_deferredlazyseek</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stdio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fflush
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf
</UL>

<P><STRONG><a name="[e3]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
</UL>

<P><STRONG><a name="[e2]"></a>__rt_SIGRTRED_inner</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtred_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTRED_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED
</UL>

<P><STRONG><a name="[d2]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_open
</UL>

<P><STRONG><a name="[c0]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>

<P><STRONG><a name="[e8]"></a>__Heap_Initialize</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, h1_init.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[7]"></a>__Heap_DescSize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, h1_init.o(.text), UNUSED)

<P><STRONG><a name="[e5]"></a>__Heap_ProvideMemory</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, h1_extend.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
</UL>

<P><STRONG><a name="[e9]"></a>_ftell_internal</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, ftell.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ftell_internal
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fseek
</UL>

<P><STRONG><a name="[14a]"></a>ftell</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ftell.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTRED_inner
</UL>

<P><STRONG><a name="[e7]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Broken
</UL>

<P><STRONG><a name="[ed]"></a>_ttywrch</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, sys_wrch.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[ee]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display &rArr; _ttywrch
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[d]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[ef]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32h7xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fc]"></a>HAL_GetREVID</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_GetREVID))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[f0]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
</UL>

<P><STRONG><a name="[10f]"></a>HAL_HSEM_FastTake</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[110]"></a>HAL_HSEM_Release</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[f1]"></a>HAL_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32h7xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f4]"></a>HAL_InitTick</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32h7xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[10d]"></a>HAL_MPU_ConfigRegion</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_MPU_ConfigRegion
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10c]"></a>HAL_MPU_Disable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10e]"></a>HAL_MPU_Enable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f5]"></a>HAL_MspInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32h7xx_hal_msp_1.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f7]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[f2]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[f9]"></a>HAL_PWREx_ConfigSupply</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PWREx_ConfigSupply
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[fa]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 580 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f3]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 278 bytes, Stack size 16 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[fb]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1410 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetREVID
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[f6]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[b]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[fe]"></a>SystemClock_Config</STRONG> (Thumb, 208 bytes, Stack size 128 bytes, main_1.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>SystemInit</STRONG> (Thumb, 208 bytes, Stack size 20 bytes, system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(.text)
</UL>
<P><STRONG><a name="[e]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[104]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[ff]"></a>__hardfp_cosf</STRONG> (Thumb, 276 bytes, Stack size 8 bytes, cosf.o(i.__hardfp_cosf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = __hardfp_cosf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gauge
</UL>

<P><STRONG><a name="[103]"></a>__hardfp_sinf</STRONG> (Thumb, 344 bytes, Stack size 16 bytes, sinf.o(i.__hardfp_sinf))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __hardfp_sinf &rArr; __mathlib_rredf2
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_rredf2
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_invalid
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gauge
</UL>

<P><STRONG><a name="[102]"></a>__mathlib_flt_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[101]"></a>__mathlib_flt_invalid</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_invalid))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[105]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
</UL>

<P><STRONG><a name="[100]"></a>__mathlib_rredf2</STRONG> (Thumb, 316 bytes, Stack size 20 bytes, rredf.o(i.__mathlib_rredf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __mathlib_rredf2
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>

<P><STRONG><a name="[106]"></a>clear_gauge_center</STRONG> (Thumb, 70 bytes, Stack size 32 bytes, main_1.o(i.clear_gauge_center))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = clear_gauge_center &rArr; lcd_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
</UL>

<P><STRONG><a name="[108]"></a>draw_gauge</STRONG> (Thumb, 340 bytes, Stack size 72 bytes, main_1.o(i.draw_gauge))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = draw_gauge &rArr; draw_simple_circle &rArr; lcd_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_line
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_circle
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sinf
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cosf
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
</UL>

<P><STRONG><a name="[109]"></a>draw_simple_circle</STRONG> (Thumb, 208 bytes, Stack size 60 bytes, main_1.o(i.draw_simple_circle))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = draw_simple_circle &rArr; lcd_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gauge
</UL>

<P><STRONG><a name="[10a]"></a>draw_simple_line</STRONG> (Thumb, 136 bytes, Stack size 44 bytes, main_1.o(i.draw_simple_line))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = draw_simple_line &rArr; lcd_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gauge
</UL>

<P><STRONG><a name="[a5]"></a>fputc</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, fputc.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = fputc &rArr; __flsbuf_byte
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__flsbuf_byte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[112]"></a>lcd_clear_screen</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, main_1.o(i.lcd_clear_screen))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
</UL>

<P><STRONG><a name="[107]"></a>lcd_draw_point</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, main_1.o(i.lcd_draw_point))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lcd_draw_point
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_line
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_simple_circle
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_gauge_center
</UL>

<P><STRONG><a name="[bb]"></a>main</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, main_1.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 224 + Unknown Stack Size
<LI>Call Chain = main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Enable
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Disable
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_ConfigRegion
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HSEM_Release
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HSEM_FastTake
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_dual_gauge_demo
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[111]"></a>simple_dual_gauge_demo</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, main_1.o(i.simple_dual_gauge_demo))
<BR><BR>[Stack]<UL><LI>Max Depth = 180 + Unknown Stack Size
<LI>Call Chain = simple_dual_gauge_demo &rArr; draw_gauge &rArr; draw_simple_circle &rArr; lcd_draw_point
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lcd_clear_screen
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;draw_gauge
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;clear_gauge_center
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[af]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[14b]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[14c]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[f8]"></a>__NVIC_SetPriority</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[a6]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
