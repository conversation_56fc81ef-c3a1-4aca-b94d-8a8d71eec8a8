<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [LCD_CM7\LCD_CM7.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image LCD_CM7\LCD_CM7.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Jul 04 09:29:36 2025
<BR><P>
<H3>Maximum Stack Usage =        224 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[b1]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[86]">ADC3_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[86]">ADC3_IRQHandler</a><BR>
 <LI><a href="#[6]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">BusFault_Handler</a><BR>
 <LI><a href="#[4]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">HardFault_Handler</a><BR>
 <LI><a href="#[5]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">MemManage_Handler</a><BR>
 <LI><a href="#[3]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">NMI_Handler</a><BR>
 <LI><a href="#[7]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">UsageFault_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[86]">ADC3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1e]">ADC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[88]">BDMA_Channel0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[89]">BDMA_Channel1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8a]">BDMA_Channel2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8b]">BDMA_Channel3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8c]">BDMA_Channel4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8d]">BDMA_Channel5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8e]">BDMA_Channel6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8f]">BDMA_Channel7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from stm32h7xx_it_1.o(i.BusFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[66]">CEC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4c]">CM4_SEV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4b]">CM7_SEV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[90]">COMP1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[97]">CRS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[57]">DCMI_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[76]">DFSDM1_FLT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[77]">DFSDM1_FLT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[78]">DFSDM1_FLT2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[79]">DFSDM1_FLT3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[17]">DMA1_Stream0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[18]">DMA1_Stream1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[19]">DMA1_Stream2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3a]">DMA1_Stream7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[62]">DMA2D_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[43]">DMA2_Stream0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[44]">DMA2_Stream1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[45]">DMA2_Stream2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[46]">DMA2_Stream3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[47]">DMA2_Stream4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6e]">DMAMUX1_OVR_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[87]">DMAMUX2_OVR_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from stm32h7xx_it_1.o(i.DebugMon_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[98]">ECC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[48]">ETH_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[49]">ETH_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[12]">EXTI0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[34]">EXTI15_10_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[13]">EXTI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[14]">EXTI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[15]">EXTI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[16]">EXTI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[23]">EXTI9_5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[1f]">FDCAN1_IT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[21]">FDCAN1_IT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[20]">FDCAN2_IT0_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[22]">FDCAN2_IT1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4a]">FDCAN_CAL_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[10]">FLASH_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3b]">FMC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[59]">FPU_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9a]">HOLD_CORE_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[75]">HRTIM1_FLT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6f]">HRTIM1_Master_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[70]">HRTIM1_TIMA_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[71]">HRTIM1_TIMB_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[72]">HRTIM1_TIMC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[73]">HRTIM1_TIMD_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[74]">HRTIM1_TIME_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[84]">HSEM1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[85]">HSEM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from stm32h7xx_it_1.o(i.HardFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2c]">I2C1_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2b]">I2C1_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2e]">I2C2_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2d]">I2C2_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[52]">I2C3_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[51]">I2C3_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[68]">I2C4_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[67]">I2C4_EV_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[81]">JPEG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[65]">LPTIM1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[91]">LPTIM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[92]">LPTIM3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[93]">LPTIM4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[94]">LPTIM5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[95]">LPUART1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[61]">LTDC_ER_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[60]">LTDC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[80]">MDIOS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7f]">MDIOS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[82]">MDMA_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from stm32h7xx_it_1.o(i.MemManage_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from stm32h7xx_it_1.o(i.NMI_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6b]">OTG_FS_EP1_IN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6a]">OTG_FS_EP1_OUT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6d]">OTG_FS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[6c]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[54]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[53]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[56]">OTG_HS_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[55]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[d]">PVD_AVD_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from stm32h7xx_it_1.o(i.PendSV_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[64]">QUADSPI_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[11]">RCC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[58]">RNG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[35]">RTC_Alarm_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[f]">RTC_WKUP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5f]">SAI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[63]">SAI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7a]">SAI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[99]">SAI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3c]">SDMMC1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[83]">SDMMC2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[69]">SPDIF_RX_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2f]">SPI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[30]">SPI2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3e]">SPI3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5c]">SPI4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5d]">SPI5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5e]">SPI6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from stm32h7xx_it_1.o(i.SVC_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7b]">SWPMI1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from stm32h7xx_it_1.o(i.SysTick_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9c]">SystemInit</a> from system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit) referenced from startup_stm32h745xx_cm7.o(.text)
 <LI><a href="#[e]">TAMP_STAMP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7c]">TIM15_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7d]">TIM16_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7e]">TIM17_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[24]">TIM1_BRK_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[27]">TIM1_CC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[26]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[25]">TIM1_UP_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[28]">TIM2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[29]">TIM3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[2a]">TIM4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3d]">TIM5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[41]">TIM6_DAC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[42]">TIM7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[36]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[39]">TIM8_CC_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[38]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[37]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[3f]">UART4_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[40]">UART5_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5a]">UART7_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[5b]">UART8_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[31]">USART1_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[32]">USART2_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[33]">USART3_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[50]">USART6_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from stm32h7xx_it_1.o(i.UsageFault_Handler) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9b]">WAKEUP_PIN_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[c]">WWDG_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[96]">WWDG_RST_IRQHandler</a> from startup_stm32h745xx_cm7.o(.text) referenced from startup_stm32h745xx_cm7.o(RESET)
 <LI><a href="#[9e]">__main</a> from __main.o(!!!main) referenced from startup_stm32h745xx_cm7.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[9e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[9f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[a1]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[c8]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[c9]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[a2]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[ca]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[a8]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a3]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[cb]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[cc]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[cd]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[ce]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[cf]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[d0]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[d1]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[d2]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[d3]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[d4]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[d5]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[d6]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[d7]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[d8]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[d9]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[da]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[db]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[dc]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[dd]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[de]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[ad]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[df]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[e0]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[e1]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[e2]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[e3]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[e4]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[e5]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[e6]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[a0]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[e7]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[a5]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[a7]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[e8]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[a9]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 224 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[e9]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[b2]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[ac]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[ea]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[ae]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC3_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>BDMA_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>BDMA_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>BDMA_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>BDMA_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>BDMA_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>BDMA_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>BDMA_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>BDMA_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CM4_SEV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CM7_SEV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>COMP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[97]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>DFSDM1_FLT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>DFSDM1_FLT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>DFSDM1_FLT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>DFSDM1_FLT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>DMAMUX1_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>DMAMUX2_OVR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[98]"></a>ECC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FDCAN1_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>FDCAN1_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>FDCAN2_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>FDCAN2_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>FDCAN_CAL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9a]"></a>HOLD_CORE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>HRTIM1_FLT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>HRTIM1_Master_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>HRTIM1_TIMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>HRTIM1_TIMB_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>HRTIM1_TIMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>HRTIM1_TIMD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>HRTIM1_TIME_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>HSEM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>HSEM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>I2C4_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>I2C4_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>JPEG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[91]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>LPTIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>LPTIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>LPTIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[95]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>MDIOS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>MDIOS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>MDMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>OTG_FS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>OTG_FS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>PVD_AVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>QUADSPI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SAI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>SAI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[99]"></a>SAI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDMMC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>SDMMC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>SPDIF_RX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>SWPMI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[9b]"></a>WAKEUP_PIN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[96]"></a>WWDG_RST_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h745xx_cm7.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[b1]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32h745xx_cm7.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[c2]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[eb]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[ed]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[ee]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[ef]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[f0]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[a6]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[ab]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[f1]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[b0]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[f2]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[f3]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[f5]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>HAL_GetREVID</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_GetREVID))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
</UL>

<P><STRONG><a name="[bc]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
</UL>

<P><STRONG><a name="[c6]"></a>HAL_HSEM_FastTake</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c7]"></a>HAL_HSEM_Release</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c0]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32h7xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[b3]"></a>HAL_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, stm32h7xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b6]"></a>HAL_InitTick</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32h7xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[c4]"></a>HAL_MPU_ConfigRegion</STRONG> (Thumb, 86 bytes, Stack size 20 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_MPU_ConfigRegion
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c3]"></a>HAL_MPU_Disable</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c5]"></a>HAL_MPU_Enable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable))
<BR><BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b7]"></a>HAL_MspInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32h7xx_hal_msp_1.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[b9]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[b4]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[bb]"></a>HAL_PWREx_ConfigSupply</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_PWREx_ConfigSupply
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[bd]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 580 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[b5]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 278 bytes, Stack size 16 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[be]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 1410 bytes, Stack size 40 bytes, stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetREVID
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[b8]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[c1]"></a>SystemClock_Config</STRONG> (Thumb, 208 bytes, Stack size 128 bytes, main_1.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_ConfigSupply
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>SystemInit</STRONG> (Thumb, 208 bytes, Stack size 20 bytes, system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(.text)
</UL>
<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h7xx_it_1.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h745xx_cm7.o(RESET)
</UL>
<P><STRONG><a name="[aa]"></a>main</STRONG> (Thumb, 172 bytes, Stack size 24 bytes, main_1.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = main &rArr; SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_Disable
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MPU_ConfigRegion
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HSEM_Release
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_HSEM_FastTake
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[a4]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[f6]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[f7]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[ba]"></a>__NVIC_SetPriority</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
