/**
 ******************************************************************************
 * @file           : simple_gauge.h
 * @brief          : 简化双仪表盘演示程序头文件
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 基于现有LCD驱动的简化仪表盘实现
 ******************************************************************************
 */

#ifndef __SIMPLE_GAUGE_H
#define __SIMPLE_GAUGE_H

#include "main.h"

/* 函数声明 */
void init_dual_gauges(void);                                   // 初始化双仪表盘
void update_dual_gauges(uint16_t left_value, uint16_t right_value); // 更新双仪表盘
void dual_gauge_demo(void);                                    // 双仪表盘演示
void draw_single_gauge(uint16_t center_x, uint16_t center_y, uint16_t value); // 绘制单个仪表盘
void draw_gauge_scales(uint16_t center_x, uint16_t center_y);  // 绘制刻度
void draw_gauge_pointer(uint16_t center_x, uint16_t center_y, uint16_t value); // 绘制指针

#endif /* __SIMPLE_GAUGE_H */
