/**
 ******************************************************************************
 * @file           : simple_gauge.c
 * @brief          : 简化双仪表盘演示程序
 * <AUTHOR> STM32H745 LCD Project
 * @date           : 2025-01-04
 ******************************************************************************
 * @attention      : 基于现有LCD驱动的简化仪表盘实现
 ******************************************************************************
 */

#include "main.h"
#include <stdio.h>
#include <math.h>

/* 仪表盘配置 */
#define GAUGE_RADIUS        60      // 仪表盘半径
#define GAUGE_CENTER_Y      200     // 仪表盘Y中心坐标
#define GAUGE_LEFT_X        160     // 左侧仪表盘X中心坐标  
#define GAUGE_RIGHT_X       480     // 右侧仪表盘X中心坐标
#define GAUGE_POINTER_LEN   50      // 指针长度

/* 角度范围 */
#define ANGLE_MIN          -135     // 起始角度(-135度)
#define ANGLE_MAX           135     // 终止角度(135度)
#define ANGLE_RANGE         270     // 角度范围(270度)

/* 数学常量 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 简化的LCD函数声明 */
extern void lcd_init(void);
extern void lcd_clear(uint32_t color);
extern void lcd_draw_point(uint16_t x, uint16_t y, uint32_t color);
extern void lcd_draw_line(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
extern void lcd_draw_circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color);
extern void lcd_fill_circle(uint16_t x, uint16_t y, uint16_t r, uint16_t color);

/* 颜色定义 */
#define WHITE   0xFFFF
#define BLACK   0x0000
#define RED     0xF800
#define GREEN   0x07E0
#define BLUE    0x001F
#define YELLOW  0xFFE0

/**
 * @brief       角度转弧度
 * @param       degrees: 角度值
 * @retval      弧度值
 */
float deg_to_rad(float degrees)
{
    return degrees * M_PI / 180.0f;
}

/**
 * @brief       绘制仪表盘刻度
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标
 * @retval      无
 */
void draw_gauge_scales(uint16_t center_x, uint16_t center_y)
{
    int16_t angle;
    float rad;
    int16_t x1, y1, x2, y2;
    
    /* 绘制主刻度(每30度一个) */
    for(angle = ANGLE_MIN; angle <= ANGLE_MAX; angle += 30) {
        rad = deg_to_rad(angle);
        
        /* 外圈点 */
        x1 = center_x + (int16_t)(cosf(rad) * (GAUGE_RADIUS - 5));
        y1 = center_y + (int16_t)(sinf(rad) * (GAUGE_RADIUS - 5));
        
        /* 内圈点 */
        x2 = center_x + (int16_t)(cosf(rad) * (GAUGE_RADIUS - 15));
        y2 = center_y + (int16_t)(sinf(rad) * (GAUGE_RADIUS - 15));
        
        lcd_draw_line(x1, y1, x2, y2, WHITE);
    }
}

/**
 * @brief       绘制仪表盘指针
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标  
 * @param       value: 数值(0-100)
 * @retval      无
 */
void draw_gauge_pointer(uint16_t center_x, uint16_t center_y, uint16_t value)
{
    /* 限制数值范围 */
    if(value > 100) value = 100;
    
    /* 计算指针角度 */
    float angle = ANGLE_MIN + (ANGLE_RANGE * value) / 100.0f;
    float rad = deg_to_rad(angle);
    
    /* 计算指针端点 */
    int16_t x_end = center_x + (int16_t)(cosf(rad) * GAUGE_POINTER_LEN);
    int16_t y_end = center_y + (int16_t)(sinf(rad) * GAUGE_POINTER_LEN);
    
    /* 绘制指针 */
    lcd_draw_line(center_x, center_y, x_end, y_end, RED);
    
    /* 绘制中心圆 */
    lcd_fill_circle(center_x, center_y, 5, BLUE);
}

/**
 * @brief       清除指针区域
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标
 * @retval      无
 */
void clear_gauge_pointer(uint16_t center_x, uint16_t center_y)
{
    /* 填充指针活动区域为背景色 */
    lcd_fill_circle(center_x, center_y, GAUGE_POINTER_LEN + 5, BLACK);
    
    /* 重新绘制外圈和刻度 */
    lcd_draw_circle(center_x, center_y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(center_x, center_y);
}

/**
 * @brief       绘制单个仪表盘
 * @param       center_x: 中心X坐标
 * @param       center_y: 中心Y坐标
 * @param       value: 数值(0-100)
 * @retval      无
 */
void draw_single_gauge(uint16_t center_x, uint16_t center_y, uint16_t value)
{
    /* 清除指针区域 */
    clear_gauge_pointer(center_x, center_y);
    
    /* 绘制新指针 */
    draw_gauge_pointer(center_x, center_y, value);
}

/**
 * @brief       初始化双仪表盘
 * @param       无
 * @retval      无
 */
void init_dual_gauges(void)
{
    /* 清屏为黑色 */
    lcd_clear(BLACK);
    
    /* 绘制左侧仪表盘外圈和刻度 */
    lcd_draw_circle(GAUGE_LEFT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(GAUGE_LEFT_X, GAUGE_CENTER_Y);
    
    /* 绘制右侧仪表盘外圈和刻度 */
    lcd_draw_circle(GAUGE_RIGHT_X, GAUGE_CENTER_Y, GAUGE_RADIUS, WHITE);
    draw_gauge_scales(GAUGE_RIGHT_X, GAUGE_CENTER_Y);
    
    printf("双仪表盘初始化完成\r\n");
}

/**
 * @brief       更新双仪表盘
 * @param       left_value: 左侧数值(0-100)
 * @param       right_value: 右侧数值(0-100)
 * @retval      无
 */
void update_dual_gauges(uint16_t left_value, uint16_t right_value)
{
    draw_single_gauge(GAUGE_LEFT_X, GAUGE_CENTER_Y, left_value);
    draw_single_gauge(GAUGE_RIGHT_X, GAUGE_CENTER_Y, right_value);
}

/**
 * @brief       双仪表盘演示
 * @param       无
 * @retval      无
 */
void dual_gauge_demo(void)
{
    uint16_t speed = 0, rpm = 0;
    uint8_t speed_dir = 0, rpm_dir = 0;
    
    printf("开始双仪表盘演示...\r\n");
    
    /* 初始化仪表盘 */
    init_dual_gauges();
    
    /* 演示循环 */
    for(uint16_t i = 0; i < 200; i++) {
        /* 更新速度值 */
        if(speed_dir == 0) {
            if(speed < 100) speed += 2;
            else speed_dir = 1;
        } else {
            if(speed > 0) speed -= 2;
            else speed_dir = 0;
        }
        
        /* 更新转速值(不同步变化) */
        if(rpm_dir == 0) {
            if(rpm < 100) rpm += 3;
            else rpm_dir = 1;
        } else {
            if(rpm > 0) rpm -= 3;
            else rpm_dir = 0;
        }
        
        /* 更新双仪表盘 */
        update_dual_gauges(speed, rpm);
        
        /* 延时 */
        HAL_Delay(150);
        
        /* 每20次循环输出一次状态 */
        if(i % 20 == 0) {
            printf("Speed: %d, RPM: %d\r\n", speed, rpm);
        }
    }
    
    printf("双仪表盘演示完成\r\n");
}
