/**
 ******************************************************************************
 * @file           : main_example.c
 * @brief          : LCD驱动测试主函数示例
 ******************************************************************************
 * 使用说明：
 * 1. 将此文件内容复制到main.c中的main函数
 * 2. 确保已包含必要的头文件
 * 3. 确保系统时钟和串口已正确配置
 ******************************************************************************
 */

#include "main.h"
#include "lcd.h"
#include "lcd_test.h"
#include <stdio.h>

/**
 * @brief  主函数示例
 * @retval int
 */
int main(void)
{
    /* MCU Configuration--------------------------------------------------------*/
    
    /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
    HAL_Init();
    
    /* Configure the system clock */
    SystemClock_Config();
    
    /* Initialize all configured peripherals */
    // MX_GPIO_Init();
    // MX_USART1_UART_Init();  // 根据实际配置调整
    
    /* USER CODE BEGIN 2 */
    printf("STM32H745 LCD驱动测试开始\r\n");
    
    /* LCD综合测试 */
    lcd_comprehensive_test();
    
    printf("LCD驱动测试完成\r\n");
    /* USER CODE END 2 */
    
    /* Infinite loop */
    /* USER CODE BEGIN WHILE */
    while (1)
    {
        /* USER CODE END WHILE */
        
        /* USER CODE BEGIN 3 */
        
        /* 可以在这里添加其他应用代码 */
        HAL_Delay(1000);  // 1秒延时
        
    }
    /* USER CODE END 3 */
}
