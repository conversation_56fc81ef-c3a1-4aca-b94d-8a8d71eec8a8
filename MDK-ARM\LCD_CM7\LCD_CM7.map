Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h745xx_cm7.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h745xx_cm7.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h745xx_cm7.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h745xx_cm7.o(RESET) refers to startup_stm32h745xx_cm7.o(STACK) for __initial_sp
    startup_stm32h745xx_cm7.o(RESET) refers to startup_stm32h745xx_cm7.o(.text) for Reset_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h745xx_cm7.o(RESET) refers to stm32h7xx_it_1.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h745xx_cm7.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h745xx_cm7.o(.text) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit) for SystemInit
    startup_stm32h745xx_cm7.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h745xx_cm7.o(.text) refers to startup_stm32h745xx_cm7.o(HEAP) for Heap_Mem
    startup_stm32h745xx_cm7.o(.text) refers to startup_stm32h745xx_cm7.o(STACK) for Stack_Mem
    main_1.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main_1.o(i.SystemClock_Config) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) for HAL_PWREx_ConfigSupply
    main_1.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main_1.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main_1.o(i.clear_gauge_center) refers to main_1.o(i.lcd_draw_point) for lcd_draw_point
    main_1.o(i.draw_gauge) refers to main_1.o(i.draw_simple_circle) for draw_simple_circle
    main_1.o(i.draw_gauge) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    main_1.o(i.draw_gauge) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    main_1.o(i.draw_gauge) refers to main_1.o(i.draw_simple_line) for draw_simple_line
    main_1.o(i.draw_simple_circle) refers to main_1.o(i.lcd_draw_point) for lcd_draw_point
    main_1.o(i.draw_simple_line) refers to main_1.o(i.lcd_draw_point) for lcd_draw_point
    main_1.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable) for HAL_MPU_Disable
    main_1.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion) for HAL_MPU_ConfigRegion
    main_1.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable) for HAL_MPU_Enable
    main_1.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main_1.o(i.main) refers to main_1.o(i.SystemClock_Config) for SystemClock_Config
    main_1.o(i.main) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake) for HAL_HSEM_FastTake
    main_1.o(i.main) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release) for HAL_HSEM_Release
    main_1.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main_1.o(i.main) refers to main_1.o(i.simple_dual_gauge_demo) for simple_dual_gauge_demo
    main_1.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    main_1.o(i.simple_dual_gauge_demo) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main_1.o(i.simple_dual_gauge_demo) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main_1.o(i.simple_dual_gauge_demo) refers to _printf_dec.o(.text) for _printf_int_dec
    main_1.o(i.simple_dual_gauge_demo) refers to noretval__2printf.o(.text) for __2printf
    main_1.o(i.simple_dual_gauge_demo) refers to main_1.o(i.lcd_clear_screen) for lcd_clear_screen
    main_1.o(i.simple_dual_gauge_demo) refers to main_1.o(i.draw_gauge) for draw_gauge
    main_1.o(i.simple_dual_gauge_demo) refers to main_1.o(i.clear_gauge_center) for clear_gauge_center
    main_1.o(i.simple_dual_gauge_demo) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    stm32h7xx_it_1.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_CRC_AddSector) for FLASH_CRC_AddSector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_CRC_SelectAddress) for FLASH_CRC_SelectAddress
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback) for HAL_HSEM_FreeCallback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for .constdata
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback) for HAL_PWREx_WKUP3_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback) for HAL_PWREx_WKUP5_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp_1.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemD2Clock
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID) for HAL_GetCurrentCPUID
    system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata) for .constdata
    system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data) for .data
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main_1.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf.o(.text) for __printf
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h745xx_cm7.o(.text) for __user_initial_stackheap
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main_1.o(.rev16_text), (4 bytes).
    Removing main_1.o(.revsh_text), (4 bytes).
    Removing main_1.o(.rrx_text), (6 bytes).
    Removing main_1.o(i.Error_Handler), (4 bytes).
    Removing main_1.o(i.lcd_write_data), (12 bytes).
    Removing main_1.o(i.lcd_write_reg), (12 bytes).
    Removing stm32h7xx_it_1.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it_1.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it_1.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp_1.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp_1.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp_1.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (496 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (92 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (308 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (136 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableBootCore), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq), (328 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq), (324 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq), (324 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (452 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (712 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (2476 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config), (288 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config), (288 bytes).
    Removing stm32h7xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation), (156 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation), (80 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation), (204 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (400 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Lock), (48 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (56 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program), (192 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT), (184 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Unlock), (68 bytes).
    Removing stm32h7xx_hal_flash.o(.bss), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_CRC_AddSector), (56 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_CRC_SelectAddress), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (64 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase), (124 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (216 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC), (340 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (276 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (200 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank1), (20 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank2), (24 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (256 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (432 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank1), (44 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank2), (52 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (368 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init), (556 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32h7xx_hal_hsem.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ActivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_DeactivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback), (2 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_GetClearKey), (12 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IsSemTaken), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ReleaseAll), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_SetClearKey), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Take), (40 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift), (180 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask), (156 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask), (116 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam), (84 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_SetConfig), (544 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Abort), (864 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT), (644 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (488 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler), (1768 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Init), (964 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1118 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (368 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT), (668 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_dma.o(.constdata), (8 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (160 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (144 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (176 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (216 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (640 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (1128 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (116 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (38 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (86 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (52 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (376 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (94 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (208 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (268 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (188 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (246 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (102 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (154 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (176 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (128 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (76 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (76 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (92 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (66 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearDomainFlags), (44 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (72 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (112 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (100 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (148 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (12 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_HoldCore), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (184 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ReleaseCore), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (124 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (120 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain2DBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain2DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain2DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D2_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D2_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (70 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (58 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain2DBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain2DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain2DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM4BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableCM4BOOT), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableCM7BOOT), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableCM4BOOT), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableCM7BOOT), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32h7xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Init), (192 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (256 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (124 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (300 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (324 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (160 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (172 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (356 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (344 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (312 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (232 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAError), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR), (34 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt), (148 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITError), (288 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt), (244 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt), (592 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (316 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (524 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TransferConfig), (48 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (124 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (88 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout), (92 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter), (88 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter), (84 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (228 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending), (48 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (280 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending), (52 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (64 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (18 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (316 bytes).
    Removing system_stm32h7xx_dualcore_boot_cm4_cm7.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx_dualcore_boot_cm4_cm7.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx_dualcore_boot_cm4_cm7.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemCoreClockUpdate), (320 bytes).

429 unused section(s) (total 44860 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../CM7/Core/Src/main.c                   0x00000000   Number         0  main_1.o ABSOLUTE
    ../CM7/Core/Src/stm32h7xx_hal_msp.c      0x00000000   Number         0  stm32h7xx_hal_msp_1.o ABSOLUTE
    ../CM7/Core/Src/stm32h7xx_it.c           0x00000000   Number         0  stm32h7xx_it_1.o ABSOLUTE
    ../Common/Src/system_stm32h7xx_dualcore_boot_cm4_cm7.c 0x00000000   Number         0  system_stm32h7xx_dualcore_boot_cm4_cm7.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ..\CM7\Core\Src\main.c                   0x00000000   Number         0  main_1.o ABSOLUTE
    ..\CM7\Core\Src\stm32h7xx_hal_msp.c      0x00000000   Number         0  stm32h7xx_hal_msp_1.o ABSOLUTE
    ..\CM7\Core\Src\stm32h7xx_it.c           0x00000000   Number         0  stm32h7xx_it_1.o ABSOLUTE
    ..\Common\Src\system_stm32h7xx_dualcore_boot_cm4_cm7.c 0x00000000   Number         0  system_stm32h7xx_dualcore_boot_cm4_cm7.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32h745xx_CM7.s                0x00000000   Number         0  startup_stm32h745xx_cm7.o ABSOLUTE
    RESET                                    0x08000000   Section      664  startup_stm32h745xx_cm7.o(RESET)
    !!!main                                  0x08000298   Section        8  __main.o(!!!main)
    !!!scatter                               0x080002a0   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080002d4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080002f0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800030c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x0800030c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000312   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000316   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000318   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800031c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x0800031c   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000324   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000024          0x08000324   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000024)
    .ARM.Collect$$libinit$$00000025          0x08000328   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000328   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000328   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000328   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000328   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000328   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800032a   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800032c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800032c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000005      0x0800032c   Section        4  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    .ARM.Collect$$libshutdown$$00000006      0x08000330   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000330   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000330   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000330   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000330   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000330   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000332   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000332   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000332   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000338   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000338   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800033c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800033c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000344   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000346   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000346   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800034a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000350   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x08000350   Section       64  startup_stm32h745xx_cm7.o(.text)
    $v0                                      0x08000350   Number         0  startup_stm32h745xx_cm7.o(.text)
    .text                                    0x08000390   Section        0  noretval__2printf.o(.text)
    .text                                    0x080003a8   Section        0  __printf.o(.text)
    .text                                    0x08000410   Section        0  _printf_dec.o(.text)
    .text                                    0x08000488   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080004d6   Section        0  heapauxi.o(.text)
    .text                                    0x080004dc   Section        0  _rserrno.o(.text)
    .text                                    0x080004f2   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080005a4   Section        0  _printf_char_file.o(.text)
    .text                                    0x080005c8   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080005d0   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080005d1   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000600   Section        0  ferror.o(.text)
    .text                                    0x08000608   Section        0  initio.o(.text)
    .text                                    0x08000740   Section        0  sys_io.o(.text)
    .text                                    0x080007a8   Section        8  libspace.o(.text)
    .text                                    0x080007b0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080007fa   Section        0  h1_free.o(.text)
    .text                                    0x08000848   Section        0  flsbuf.o(.text)
    .text                                    0x08000a1e   Section        0  setvbuf.o(.text)
    .text                                    0x08000a64   Section        0  fopen.o(.text)
    _freopen_locked                          0x08000a65   Thumb Code     0  fopen.o(.text)
    .text                                    0x08000b50   Section        0  fclose.o(.text)
    .text                                    0x08000b9c   Section        0  exit.o(.text)
    .text                                    0x08000bae   Section        0  defsig_rtred_outer.o(.text)
    .text                                    0x08000bbc   Section        2  use_no_semi.o(.text)
    .text                                    0x08000bbe   Section        0  indicate_semi.o(.text)
    .text                                    0x08000bc0   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08000bc8   Section        0  hguard.o(.text)
    .text                                    0x08000bcc   Section        0  init_alloc.o(.text)
    .text                                    0x08000c56   Section        0  h1_alloc.o(.text)
    .text                                    0x08000cb4   Section        0  fseek.o(.text)
    .text                                    0x08000dac   Section        0  stdio.o(.text)
    .text                                    0x08000e9c   Section        0  defsig_exit.o(.text)
    .text                                    0x08000ea8   Section        0  defsig_rtred_inner.o(.text)
    .text                                    0x08000edc   Section        0  strlen.o(.text)
    .text                                    0x08000f1c   Section        0  sys_exit.o(.text)
    .text                                    0x08000f28   Section        0  h1_init.o(.text)
    .text                                    0x08000f36   Section        0  h1_extend.o(.text)
    .text                                    0x08000f6a   Section        0  ftell.o(.text)
    .text                                    0x08000fac   Section        0  defsig_general.o(.text)
    .text                                    0x08000fde   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08000fec   Section        0  sys_wrch.o(.text)
    .text                                    0x08000ffc   Section        0  defsig_rtmem_inner.o(.text)
    i.BusFault_Handler                       0x0800104c   Section        0  stm32h7xx_it_1.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x0800104e   Section        0  stm32h7xx_it_1.o(i.DebugMon_Handler)
    i.HAL_Delay                              0x08001050   Section        0  stm32h7xx_hal.o(i.HAL_Delay)
    i.HAL_GetREVID                           0x08001074   Section        0  stm32h7xx_hal.o(i.HAL_GetREVID)
    i.HAL_GetTick                            0x08001080   Section        0  stm32h7xx_hal.o(i.HAL_GetTick)
    i.HAL_HSEM_FastTake                      0x0800108c   Section        0  stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake)
    i.HAL_HSEM_Release                       0x080010ac   Section        0  stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release)
    i.HAL_IncTick                            0x080010c0   Section        0  stm32h7xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080010d0   Section        0  stm32h7xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800112c   Section        0  stm32h7xx_hal.o(i.HAL_InitTick)
    i.HAL_MPU_ConfigRegion                   0x0800116c   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    i.HAL_MPU_Disable                        0x080011c8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    i.HAL_MPU_Enable                         0x080011e4   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    i.HAL_MspInit                            0x08001208   Section        0  stm32h7xx_hal_msp_1.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x08001224   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001264   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ConfigSupply                 0x08001288   Section        0  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    i.HAL_RCC_ClockConfig                    0x0800130c   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetSysClockFreq                0x08001568   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080016a0   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001c22   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HardFault_Handler                      0x08001c48   Section        0  stm32h7xx_it_1.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08001c4a   Section        0  stm32h7xx_it_1.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001c4c   Section        0  stm32h7xx_it_1.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08001c4e   Section        0  stm32h7xx_it_1.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08001c50   Section        0  stm32h7xx_it_1.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001c52   Section        0  stm32h7xx_it_1.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001c58   Section        0  main_1.o(i.SystemClock_Config)
    i.SystemInit                             0x08001d34   Section        0  system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit)
    i.UsageFault_Handler                     0x08001e2c   Section        0  stm32h7xx_it_1.o(i.UsageFault_Handler)
    i.__ARM_fpclassifyf                      0x08001e2e   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_SetPriority                     0x08001e54   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08001e55   Thumb Code    34  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_cosf                          0x08001e78   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_sinf                          0x08001fc8   Section        0  sinf.o(i.__hardfp_sinf)
    i.__mathlib_flt_infnan                   0x0800215c   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08002164   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08002174   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08002184   Section        0  rredf.o(i.__mathlib_rredf2)
    i.clear_gauge_center                     0x080022d8   Section        0  main_1.o(i.clear_gauge_center)
    i.draw_gauge                             0x08002320   Section        0  main_1.o(i.draw_gauge)
    i.draw_simple_circle                     0x0800248c   Section        0  main_1.o(i.draw_simple_circle)
    i.draw_simple_line                       0x0800255c   Section        0  main_1.o(i.draw_simple_line)
    i.fputc                                  0x080025e4   Section        0  fputc.o(i.fputc)
    i.lcd_clear_screen                       0x08002600   Section        0  main_1.o(i.lcd_clear_screen)
    i.lcd_draw_point                         0x08002640   Section        0  main_1.o(i.lcd_draw_point)
    i.main                                   0x08002678   Section        0  main_1.o(i.main)
    i.simple_dual_gauge_demo                 0x08002768   Section        0  main_1.o(i.simple_dual_gauge_demo)
    x$fpl$fpinit                             0x080028a4   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080028a4   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x080028ae   Section       16  system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata)
    x$fpl$usenofp                            0x080028ae   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080028c0   Section       32  rredf.o(.constdata)
    twooverpi                                0x080028c0   Data          32  rredf.o(.constdata)
    .constdata                               0x080028e0   Section        4  sys_io.o(.constdata)
    .constdata                               0x080028e4   Section        4  sys_io.o(.constdata)
    .constdata                               0x080028e8   Section        4  sys_io.o(.constdata)
    .data                                    0x20000000   Section       12  stm32h7xx_hal.o(.data)
    .data                                    0x2000000c   Section        8  system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data)
    .data                                    0x20000014   Section        4  stdio_streams.o(.data)
    .data                                    0x20000018   Section        4  stdio_streams.o(.data)
    .data                                    0x2000001c   Section        4  stdio_streams.o(.data)
    .bss                                     0x20000020   Section       84  stdio_streams.o(.bss)
    .bss                                     0x20000074   Section       84  stdio_streams.o(.bss)
    .bss                                     0x200000c8   Section       84  stdio_streams.o(.bss)
    .bss                                     0x2000011c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000180   Section      512  startup_stm32h745xx_cm7.o(HEAP)
    Heap_Mem                                 0x20000180   Data         512  startup_stm32h745xx_cm7.o(HEAP)
    STACK                                    0x20000380   Section     1024  startup_stm32h745xx_cm7.o(STACK)
    Stack_Mem                                0x20000380   Data        1024  startup_stm32h745xx_cm7.o(STACK)
    __initial_sp                             0x20000780   Data           0  startup_stm32h745xx_cm7.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main_1.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x00000298   Number         0  startup_stm32h745xx_cm7.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h745xx_cm7.o(RESET)
    __Vectors_End                            0x08000298   Data           0  startup_stm32h745xx_cm7.o(RESET)
    __main                                   0x08000299   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080002a1   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080002af   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080002d5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080002f1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x0800030d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x0800030d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000313   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000317   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000319   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x0800031d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x0800031d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_atexit_1                   0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_fp_trap_1                  0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_rand_1                     0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_signal_1                   0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_2                    0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000024)
    __rt_lib_init_user_alloc_1               0x08000325   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_alloca_1                   0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_cpp_1                      0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_return                     0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_stdio_1                    0x08000329   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800032b   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800032d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800032d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_2                0x0800032d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000005)
    __rt_lib_shutdown_fp_trap_1              0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000331   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000333   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000333   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000333   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000339   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000339   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800033d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800033d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000345   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000347   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000347   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800034b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000351   Thumb Code     8  startup_stm32h745xx_cm7.o(.text)
    _maybe_terminate_alloc                   0x08000351   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC3_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    ADC_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel0_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel1_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel2_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel3_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel4_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel5_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel6_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    BDMA_Channel7_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    CEC_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    CM4_SEV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    CM7_SEV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    COMP1_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    CRS_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DCMI_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2D_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    ECC_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    ETH_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    ETH_WKUP_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI0_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI15_10_IRQHandler                     0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI1_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI2_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI3_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI4_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    EXTI9_5_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FDCAN1_IT0_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FDCAN1_IT1_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FDCAN2_IT0_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FDCAN2_IT1_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FDCAN_CAL_IRQHandler                     0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FLASH_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FMC_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    FPU_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HOLD_CORE_IRQHandler                     0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_FLT_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_Master_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_TIMA_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_TIMB_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_TIMC_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_TIMD_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HRTIM1_TIME_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HSEM1_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    HSEM2_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C1_ER_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C1_EV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C2_ER_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C2_EV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C3_ER_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C3_EV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C4_ER_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    I2C4_EV_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    JPEG_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPTIM1_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPTIM2_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPTIM3_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPTIM4_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPTIM5_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LPUART1_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LTDC_ER_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    LTDC_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    MDIOS_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    MDIOS_WKUP_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    MDMA_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_FS_EP1_IN_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_FS_EP1_OUT_IRQHandler                0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_FS_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_HS_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    PVD_AVD_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    QUADSPI_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    RCC_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    RNG_IRQHandler                           0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    RTC_Alarm_IRQHandler                     0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    RTC_WKUP_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SAI1_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SAI2_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SAI3_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SAI4_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SDMMC1_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SDMMC2_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPDIF_RX_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI1_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI2_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI3_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI4_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI5_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SPI6_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    SWPMI1_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM15_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM16_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM17_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM1_BRK_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM1_CC_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM1_UP_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM2_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM3_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM4_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM5_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM6_DAC_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM7_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM8_CC_IRQHandler                       0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    UART4_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    UART5_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    UART7_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    UART8_IRQHandler                         0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    USART1_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    USART2_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    USART3_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    USART6_IRQHandler                        0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    WAKEUP_PIN_IRQHandler                    0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    WWDG_IRQHandler                          0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    WWDG_RST_IRQHandler                      0x0800036b   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    __user_initial_stackheap                 0x0800036d   Thumb Code     0  startup_stm32h745xx_cm7.o(.text)
    __2printf                                0x08000391   Thumb Code    20  noretval__2printf.o(.text)
    __printf                                 0x080003a9   Thumb Code   104  __printf.o(.text)
    _printf_int_dec                          0x08000411   Thumb Code   104  _printf_dec.o(.text)
    __aeabi_memclr4                          0x08000489   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000489   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000489   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800048d   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080004d7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080004d9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080004db   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080004dd   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080004e7   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x080004f3   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_file                        0x080005a5   Thumb Code    32  _printf_char_file.o(.text)
    __aeabi_errno_addr                       0x080005c9   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080005c9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080005c9   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _printf_char_common                      0x080005db   Thumb Code    32  _printf_char_common.o(.text)
    ferror                                   0x08000601   Thumb Code     8  ferror.o(.text)
    _initio                                  0x08000609   Thumb Code   210  initio.o(.text)
    _terminateio                             0x080006db   Thumb Code    56  initio.o(.text)
    _sys_open                                0x08000741   Thumb Code    20  sys_io.o(.text)
    _sys_close                               0x08000755   Thumb Code    12  sys_io.o(.text)
    _sys_write                               0x08000761   Thumb Code    16  sys_io.o(.text)
    _sys_read                                0x08000771   Thumb Code    14  sys_io.o(.text)
    _sys_istty                               0x0800077f   Thumb Code    12  sys_io.o(.text)
    _sys_seek                                0x0800078b   Thumb Code    14  sys_io.o(.text)
    _sys_ensure                              0x08000799   Thumb Code     2  sys_io.o(.text)
    _sys_flen                                0x0800079b   Thumb Code    12  sys_io.o(.text)
    __user_libspace                          0x080007a9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080007a9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080007a9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080007b1   Thumb Code    74  sys_stackheap_outer.o(.text)
    free                                     0x080007fb   Thumb Code    78  h1_free.o(.text)
    __flsbuf                                 0x08000849   Thumb Code   470  flsbuf.o(.text)
    __flsbuf_byte                            0x08000849   Thumb Code     0  flsbuf.o(.text)
    __flsbuf_wide                            0x08000849   Thumb Code     0  flsbuf.o(.text)
    setvbuf                                  0x08000a1f   Thumb Code    70  setvbuf.o(.text)
    freopen                                  0x08000a65   Thumb Code   158  fopen.o(.text)
    fopen                                    0x08000b03   Thumb Code    74  fopen.o(.text)
    _fclose_internal                         0x08000b51   Thumb Code    76  fclose.o(.text)
    fclose                                   0x08000b51   Thumb Code     0  fclose.o(.text)
    exit                                     0x08000b9d   Thumb Code    18  exit.o(.text)
    __rt_SIGRTRED                            0x08000baf   Thumb Code    14  defsig_rtred_outer.o(.text)
    __I$use$semihosting                      0x08000bbd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000bbd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000bbf   Thumb Code     0  indicate_semi.o(.text)
    __rt_heap_descriptor                     0x08000bc1   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08000bc9   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08000bcb   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08000bcd   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x08000bcf   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x08000bd1   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x08000bf3   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08000bf9   Thumb Code    94  init_alloc.o(.text)
    malloc                                   0x08000c57   Thumb Code    94  h1_alloc.o(.text)
    _fseek                                   0x08000cb5   Thumb Code   242  fseek.o(.text)
    fseek                                    0x08000cb5   Thumb Code     0  fseek.o(.text)
    _seterr                                  0x08000dad   Thumb Code    20  stdio.o(.text)
    _writebuf                                0x08000dc1   Thumb Code    84  stdio.o(.text)
    _fflush                                  0x08000e15   Thumb Code    70  stdio.o(.text)
    _deferredlazyseek                        0x08000e5b   Thumb Code    60  stdio.o(.text)
    __sig_exit                               0x08000e9d   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTRED_inner                      0x08000ea9   Thumb Code    14  defsig_rtred_inner.o(.text)
    strlen                                   0x08000edd   Thumb Code    62  strlen.o(.text)
    _sys_exit                                0x08000f1d   Thumb Code     8  sys_exit.o(.text)
    __Heap_Initialize                        0x08000f29   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x08000f33   Thumb Code     4  h1_init.o(.text)
    __Heap_ProvideMemory                     0x08000f37   Thumb Code    52  h1_extend.o(.text)
    _ftell_internal                          0x08000f6b   Thumb Code    66  ftell.o(.text)
    ftell                                    0x08000f6b   Thumb Code     0  ftell.o(.text)
    __default_signal_display                 0x08000fad   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTMEM                            0x08000fdf   Thumb Code    14  defsig_rtmem_outer.o(.text)
    _ttywrch                                 0x08000fed   Thumb Code    14  sys_wrch.o(.text)
    __rt_SIGRTMEM_inner                      0x08000ffd   Thumb Code    22  defsig_rtmem_inner.o(.text)
    BusFault_Handler                         0x0800104d   Thumb Code     2  stm32h7xx_it_1.o(i.BusFault_Handler)
    DebugMon_Handler                         0x0800104f   Thumb Code     2  stm32h7xx_it_1.o(i.DebugMon_Handler)
    HAL_Delay                                0x08001051   Thumb Code    32  stm32h7xx_hal.o(i.HAL_Delay)
    HAL_GetREVID                             0x08001075   Thumb Code     8  stm32h7xx_hal.o(i.HAL_GetREVID)
    HAL_GetTick                              0x08001081   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetTick)
    HAL_HSEM_FastTake                        0x0800108d   Thumb Code    24  stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake)
    HAL_HSEM_Release                         0x080010ad   Thumb Code    16  stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release)
    HAL_IncTick                              0x080010c1   Thumb Code    12  stm32h7xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080010d1   Thumb Code    74  stm32h7xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800112d   Thumb Code    56  stm32h7xx_hal.o(i.HAL_InitTick)
    HAL_MPU_ConfigRegion                     0x0800116d   Thumb Code    86  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    HAL_MPU_Disable                          0x080011c9   Thumb Code    24  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    HAL_MPU_Enable                           0x080011e5   Thumb Code    30  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    HAL_MspInit                              0x08001209   Thumb Code    22  stm32h7xx_hal_msp_1.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x08001225   Thumb Code    60  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001265   Thumb Code    28  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ConfigSupply                   0x08001289   Thumb Code   128  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    HAL_RCC_ClockConfig                      0x0800130d   Thumb Code   580  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08001569   Thumb Code   278  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080016a1   Thumb Code  1410  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001c23   Thumb Code    38  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HardFault_Handler                        0x08001c49   Thumb Code     2  stm32h7xx_it_1.o(i.HardFault_Handler)
    MemManage_Handler                        0x08001c4b   Thumb Code     2  stm32h7xx_it_1.o(i.MemManage_Handler)
    NMI_Handler                              0x08001c4d   Thumb Code     2  stm32h7xx_it_1.o(i.NMI_Handler)
    PendSV_Handler                           0x08001c4f   Thumb Code     2  stm32h7xx_it_1.o(i.PendSV_Handler)
    SVC_Handler                              0x08001c51   Thumb Code     2  stm32h7xx_it_1.o(i.SVC_Handler)
    SysTick_Handler                          0x08001c53   Thumb Code     4  stm32h7xx_it_1.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001c59   Thumb Code   208  main_1.o(i.SystemClock_Config)
    SystemInit                               0x08001d35   Thumb Code   208  system_stm32h7xx_dualcore_boot_cm4_cm7.o(i.SystemInit)
    UsageFault_Handler                       0x08001e2d   Thumb Code     2  stm32h7xx_it_1.o(i.UsageFault_Handler)
    __ARM_fpclassifyf                        0x08001e2f   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_cosf                            0x08001e79   Thumb Code   276  cosf.o(i.__hardfp_cosf)
    __hardfp_sinf                            0x08001fc9   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __mathlib_flt_infnan                     0x0800215d   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08002165   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08002175   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08002185   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    clear_gauge_center                       0x080022d9   Thumb Code    70  main_1.o(i.clear_gauge_center)
    draw_gauge                               0x08002321   Thumb Code   340  main_1.o(i.draw_gauge)
    draw_simple_circle                       0x0800248d   Thumb Code   208  main_1.o(i.draw_simple_circle)
    draw_simple_line                         0x0800255d   Thumb Code   136  main_1.o(i.draw_simple_line)
    fputc                                    0x080025e5   Thumb Code    26  fputc.o(i.fputc)
    lcd_clear_screen                         0x08002601   Thumb Code    60  main_1.o(i.lcd_clear_screen)
    lcd_draw_point                           0x08002641   Thumb Code    50  main_1.o(i.lcd_draw_point)
    main                                     0x08002679   Thumb Code   192  main_1.o(i.main)
    simple_dual_gauge_demo                   0x08002769   Thumb Code   196  main_1.o(i.simple_dual_gauge_demo)
    _fp_init                                 0x080028a5   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080028ad   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080028ad   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    D1CorePrescTable                         0x080028ae   Data          16  system_stm32h7xx_dualcore_boot_cm4_cm7.o(.constdata)
    __I$use$fp                               0x080028ae   Number         0  usenofp.o(x$fpl$usenofp)
    __stdin_name                             0x080028e0   Data           4  sys_io.o(.constdata)
    __stdout_name                            0x080028e4   Data           4  sys_io.o(.constdata)
    __stderr_name                            0x080028e8   Data           4  sys_io.o(.constdata)
    Region$$Table$$Base                      0x080028ec   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800290c   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000000   Data           1  stm32h7xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32h7xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32h7xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data)
    SystemD2Clock                            0x20000010   Data           4  system_stm32h7xx_dualcore_boot_cm4_cm7.o(.data)
    __aeabi_stdin                            0x20000014   Data           4  stdio_streams.o(.data)
    __aeabi_stdout                           0x20000018   Data           4  stdio_streams.o(.data)
    __aeabi_stderr                           0x2000001c   Data           4  stdio_streams.o(.data)
    __stdin                                  0x20000020   Data          84  stdio_streams.o(.bss)
    __stdout                                 0x20000074   Data          84  stdio_streams.o(.bss)
    __stderr                                 0x200000c8   Data          84  stdio_streams.o(.bss)
    __libspace_start                         0x2000011c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000017c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000299

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000292c, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000290c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000298   Data   RO            3    RESET               startup_stm32h745xx_cm7.o
    0x08000298   0x08000298   0x00000008   Code   RO         2926  * !!!main             c_w.l(__main.o)
    0x080002a0   0x080002a0   0x00000034   Code   RO         3323    !!!scatter          c_w.l(__scatter.o)
    0x080002d4   0x080002d4   0x0000001a   Code   RO         3325    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080002ee   0x080002ee   0x00000002   PAD
    0x080002f0   0x080002f0   0x0000001c   Code   RO         3327    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800030c   0x0800030c   0x00000000   Code   RO         2921    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         2920    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000312   0x08000312   0x00000004   Code   RO         2959    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000316   0x08000316   0x00000002   Code   RO         3116    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000318   0x08000318   0x00000004   Code   RO         3117    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800031c   0x0800031c   0x00000000   Code   RO         3120    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800031c   0x0800031c   0x00000008   Code   RO         3121    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3123    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3125    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3127    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3130    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3132    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3134    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3136    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3138    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3140    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3142    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3144    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3146    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000000   Code   RO         3148    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000324   0x08000324   0x00000004   Code   RO         3149    .ARM.Collect$$libinit$$00000024  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000000   Code   RO         3150    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000000   Code   RO         3154    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000000   Code   RO         3156    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000000   Code   RO         3158    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000000   Code   RO         3160    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000328   0x08000328   0x00000002   Code   RO         3161    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800032a   0x0800032a   0x00000002   Code   RO         3302    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800032c   0x0800032c   0x00000000   Code   RO         3163    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800032c   0x0800032c   0x00000000   Code   RO         3165    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800032c   0x0800032c   0x00000004   Code   RO         3166    .ARM.Collect$$libshutdown$$00000005  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000000   Code   RO         3167    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000000   Code   RO         3170    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000000   Code   RO         3173    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000000   Code   RO         3175    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000000   Code   RO         3178    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000330   0x08000330   0x00000002   Code   RO         3179    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000332   0x08000332   0x00000000   Code   RO         2952    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000332   0x08000332   0x00000000   Code   RO         2987    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000332   0x08000332   0x00000006   Code   RO         2999    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000338   0x08000338   0x00000000   Code   RO         2989    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000338   0x08000338   0x00000004   Code   RO         2990    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800033c   0x0800033c   0x00000000   Code   RO         2992    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800033c   0x0800033c   0x00000008   Code   RO         2993    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000344   0x08000344   0x00000002   Code   RO         3185    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000346   0x08000346   0x00000000   Code   RO         3243    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000346   0x08000346   0x00000004   Code   RO         3244    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800034a   0x0800034a   0x00000006   Code   RO         3245    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000350   0x08000350   0x00000000   Code   RO         3249    .emb_text           c_w.l(maybetermalloc1.o)
    0x08000350   0x08000350   0x00000040   Code   RO            4    .text               startup_stm32h745xx_cm7.o
    0x08000390   0x08000390   0x00000018   Code   RO         2894    .text               c_w.l(noretval__2printf.o)
    0x080003a8   0x080003a8   0x00000068   Code   RO         2896    .text               c_w.l(__printf.o)
    0x08000410   0x08000410   0x00000078   Code   RO         2898    .text               c_w.l(_printf_dec.o)
    0x08000488   0x08000488   0x0000004e   Code   RO         2922    .text               c_w.l(rt_memclr_w.o)
    0x080004d6   0x080004d6   0x00000006   Code   RO         2924    .text               c_w.l(heapauxi.o)
    0x080004dc   0x080004dc   0x00000016   Code   RO         2953    .text               c_w.l(_rserrno.o)
    0x080004f2   0x080004f2   0x000000b2   Code   RO         2955    .text               c_w.l(_printf_intcommon.o)
    0x080005a4   0x080005a4   0x00000024   Code   RO         2957    .text               c_w.l(_printf_char_file.o)
    0x080005c8   0x080005c8   0x00000008   Code   RO         3004    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080005d0   0x080005d0   0x00000030   Code   RO         3006    .text               c_w.l(_printf_char_common.o)
    0x08000600   0x08000600   0x00000008   Code   RO         3008    .text               c_w.l(ferror.o)
    0x08000608   0x08000608   0x00000138   Code   RO         3014    .text               c_w.l(initio.o)
    0x08000740   0x08000740   0x00000066   Code   RO         3022    .text               c_w.l(sys_io.o)
    0x080007a6   0x080007a6   0x00000002   PAD
    0x080007a8   0x080007a8   0x00000008   Code   RO         3027    .text               c_w.l(libspace.o)
    0x080007b0   0x080007b0   0x0000004a   Code   RO         3032    .text               c_w.l(sys_stackheap_outer.o)
    0x080007fa   0x080007fa   0x0000004e   Code   RO         3036    .text               c_w.l(h1_free.o)
    0x08000848   0x08000848   0x000001d6   Code   RO         3092    .text               c_w.l(flsbuf.o)
    0x08000a1e   0x08000a1e   0x00000046   Code   RO         3094    .text               c_w.l(setvbuf.o)
    0x08000a64   0x08000a64   0x000000ec   Code   RO         3097    .text               c_w.l(fopen.o)
    0x08000b50   0x08000b50   0x0000004c   Code   RO         3099    .text               c_w.l(fclose.o)
    0x08000b9c   0x08000b9c   0x00000012   Code   RO         3105    .text               c_w.l(exit.o)
    0x08000bae   0x08000bae   0x0000000e   Code   RO         3107    .text               c_w.l(defsig_rtred_outer.o)
    0x08000bbc   0x08000bbc   0x00000002   Code   RO         3182    .text               c_w.l(use_no_semi.o)
    0x08000bbe   0x08000bbe   0x00000000   Code   RO         3184    .text               c_w.l(indicate_semi.o)
    0x08000bbe   0x08000bbe   0x00000002   PAD
    0x08000bc0   0x08000bc0   0x00000008   Code   RO         3192    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08000bc8   0x08000bc8   0x00000004   Code   RO         3194    .text               c_w.l(hguard.o)
    0x08000bcc   0x08000bcc   0x0000008a   Code   RO         3196    .text               c_w.l(init_alloc.o)
    0x08000c56   0x08000c56   0x0000005e   Code   RO         3202    .text               c_w.l(h1_alloc.o)
    0x08000cb4   0x08000cb4   0x000000f8   Code   RO         3218    .text               c_w.l(fseek.o)
    0x08000dac   0x08000dac   0x000000f0   Code   RO         3220    .text               c_w.l(stdio.o)
    0x08000e9c   0x08000e9c   0x0000000a   Code   RO         3226    .text               c_w.l(defsig_exit.o)
    0x08000ea6   0x08000ea6   0x00000002   PAD
    0x08000ea8   0x08000ea8   0x00000034   Code   RO         3228    .text               c_w.l(defsig_rtred_inner.o)
    0x08000edc   0x08000edc   0x0000003e   Code   RO         3232    .text               c_w.l(strlen.o)
    0x08000f1a   0x08000f1a   0x00000002   PAD
    0x08000f1c   0x08000f1c   0x0000000c   Code   RO         3240    .text               c_w.l(sys_exit.o)
    0x08000f28   0x08000f28   0x0000000e   Code   RO         3251    .text               c_w.l(h1_init.o)
    0x08000f36   0x08000f36   0x00000034   Code   RO         3253    .text               c_w.l(h1_extend.o)
    0x08000f6a   0x08000f6a   0x00000042   Code   RO         3263    .text               c_w.l(ftell.o)
    0x08000fac   0x08000fac   0x00000032   Code   RO         3271    .text               c_w.l(defsig_general.o)
    0x08000fde   0x08000fde   0x0000000e   Code   RO         3273    .text               c_w.l(defsig_rtmem_outer.o)
    0x08000fec   0x08000fec   0x0000000e   Code   RO         3288    .text               c_w.l(sys_wrch.o)
    0x08000ffa   0x08000ffa   0x00000002   PAD
    0x08000ffc   0x08000ffc   0x00000050   Code   RO         3296    .text               c_w.l(defsig_rtmem_inner.o)
    0x0800104c   0x0800104c   0x00000002   Code   RO          207    i.BusFault_Handler  stm32h7xx_it_1.o
    0x0800104e   0x0800104e   0x00000002   Code   RO          208    i.DebugMon_Handler  stm32h7xx_it_1.o
    0x08001050   0x08001050   0x00000024   Code   RO         1875    i.HAL_Delay         stm32h7xx_hal.o
    0x08001074   0x08001074   0x0000000c   Code   RO         1894    i.HAL_GetREVID      stm32h7xx_hal.o
    0x08001080   0x08001080   0x0000000c   Code   RO         1895    i.HAL_GetTick       stm32h7xx_hal.o
    0x0800108c   0x0800108c   0x00000020   Code   RO         1061    i.HAL_HSEM_FastTake  stm32h7xx_hal_hsem.o
    0x080010ac   0x080010ac   0x00000014   Code   RO         1066    i.HAL_HSEM_Release  stm32h7xx_hal_hsem.o
    0x080010c0   0x080010c0   0x00000010   Code   RO         1901    i.HAL_IncTick       stm32h7xx_hal.o
    0x080010d0   0x080010d0   0x0000005c   Code   RO         1902    i.HAL_Init          stm32h7xx_hal.o
    0x0800112c   0x0800112c   0x00000040   Code   RO         1903    i.HAL_InitTick      stm32h7xx_hal.o
    0x0800116c   0x0800116c   0x0000005c   Code   RO          307    i.HAL_MPU_ConfigRegion  stm32h7xx_hal_cortex.o
    0x080011c8   0x080011c8   0x0000001c   Code   RO          308    i.HAL_MPU_Disable   stm32h7xx_hal_cortex.o
    0x080011e4   0x080011e4   0x00000024   Code   RO          310    i.HAL_MPU_Enable    stm32h7xx_hal_cortex.o
    0x08001208   0x08001208   0x0000001c   Code   RO          282    i.HAL_MspInit       stm32h7xx_hal_msp_1.o
    0x08001224   0x08001224   0x00000040   Code   RO          320    i.HAL_NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08001264   0x08001264   0x00000024   Code   RO          321    i.HAL_NVIC_SetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x08001288   0x08001288   0x00000084   Code   RO         1598    i.HAL_PWREx_ConfigSupply  stm32h7xx_hal_pwr_ex.o
    0x0800130c   0x0800130c   0x0000025c   Code   RO          496    i.HAL_RCC_ClockConfig  stm32h7xx_hal_rcc.o
    0x08001568   0x08001568   0x00000138   Code   RO          505    i.HAL_RCC_GetSysClockFreq  stm32h7xx_hal_rcc.o
    0x080016a0   0x080016a0   0x00000582   Code   RO          508    i.HAL_RCC_OscConfig  stm32h7xx_hal_rcc.o
    0x08001c22   0x08001c22   0x00000026   Code   RO          325    i.HAL_SYSTICK_Config  stm32h7xx_hal_cortex.o
    0x08001c48   0x08001c48   0x00000002   Code   RO          209    i.HardFault_Handler  stm32h7xx_it_1.o
    0x08001c4a   0x08001c4a   0x00000002   Code   RO          210    i.MemManage_Handler  stm32h7xx_it_1.o
    0x08001c4c   0x08001c4c   0x00000002   Code   RO          211    i.NMI_Handler       stm32h7xx_it_1.o
    0x08001c4e   0x08001c4e   0x00000002   Code   RO          212    i.PendSV_Handler    stm32h7xx_it_1.o
    0x08001c50   0x08001c50   0x00000002   Code   RO          213    i.SVC_Handler       stm32h7xx_it_1.o
    0x08001c52   0x08001c52   0x00000004   Code   RO          214    i.SysTick_Handler   stm32h7xx_it_1.o
    0x08001c56   0x08001c56   0x00000002   PAD
    0x08001c58   0x08001c58   0x000000dc   Code   RO           14    i.SystemClock_Config  main_1.o
    0x08001d34   0x08001d34   0x000000f8   Code   RO         2857    i.SystemInit        system_stm32h7xx_dualcore_boot_cm4_cm7.o
    0x08001e2c   0x08001e2c   0x00000002   Code   RO          215    i.UsageFault_Handler  stm32h7xx_it_1.o
    0x08001e2e   0x08001e2e   0x00000026   Code   RO         2967    i.__ARM_fpclassifyf  m_wv.l(fpclassifyf.o)
    0x08001e54   0x08001e54   0x00000022   Code   RO          327    i.__NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08001e76   0x08001e76   0x00000002   PAD
    0x08001e78   0x08001e78   0x00000150   Code   RO         2928    i.__hardfp_cosf     m_wv.l(cosf.o)
    0x08001fc8   0x08001fc8   0x00000194   Code   RO         2940    i.__hardfp_sinf     m_wv.l(sinf.o)
    0x0800215c   0x0800215c   0x00000006   Code   RO         2970    i.__mathlib_flt_infnan  m_wv.l(funder.o)
    0x08002162   0x08002162   0x00000002   PAD
    0x08002164   0x08002164   0x00000010   Code   RO         2972    i.__mathlib_flt_invalid  m_wv.l(funder.o)
    0x08002174   0x08002174   0x00000010   Code   RO         2975    i.__mathlib_flt_underflow  m_wv.l(funder.o)
    0x08002184   0x08002184   0x00000154   Code   RO         2983    i.__mathlib_rredf2  m_wv.l(rredf.o)
    0x080022d8   0x080022d8   0x00000046   Code   RO           15    i.clear_gauge_center  main_1.o
    0x0800231e   0x0800231e   0x00000002   PAD
    0x08002320   0x08002320   0x0000016c   Code   RO           16    i.draw_gauge        main_1.o
    0x0800248c   0x0800248c   0x000000d0   Code   RO           17    i.draw_simple_circle  main_1.o
    0x0800255c   0x0800255c   0x00000088   Code   RO           18    i.draw_simple_line  main_1.o
    0x080025e4   0x080025e4   0x0000001a   Code   RO         3011    i.fputc             c_w.l(fputc.o)
    0x080025fe   0x080025fe   0x00000002   PAD
    0x08002600   0x08002600   0x00000040   Code   RO           19    i.lcd_clear_screen  main_1.o
    0x08002640   0x08002640   0x00000038   Code   RO           20    i.lcd_draw_point    main_1.o
    0x08002678   0x08002678   0x000000f0   Code   RO           23    i.main              main_1.o
    0x08002768   0x08002768   0x0000013c   Code   RO           24    i.simple_dual_gauge_demo  main_1.o
    0x080028a4   0x080028a4   0x0000000a   Code   RO         3238    x$fpl$fpinit        fz_wv.l(fpinit.o)
    0x080028ae   0x080028ae   0x00000000   Code   RO         2966    x$fpl$usenofp       fz_wv.l(usenofp.o)
    0x080028ae   0x080028ae   0x00000010   Data   RO         2858    .constdata          system_stm32h7xx_dualcore_boot_cm4_cm7.o
    0x080028be   0x080028be   0x00000002   PAD
    0x080028c0   0x080028c0   0x00000020   Data   RO         2984    .constdata          m_wv.l(rredf.o)
    0x080028e0   0x080028e0   0x00000004   Data   RO         3023    .constdata          c_w.l(sys_io.o)
    0x080028e4   0x080028e4   0x00000004   Data   RO         3024    .constdata          c_w.l(sys_io.o)
    0x080028e8   0x080028e8   0x00000004   Data   RO         3025    .constdata          c_w.l(sys_io.o)
    0x080028ec   0x080028ec   0x00000020   Data   RO         3321    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800290c, Size: 0x00000780, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800290c   0x0000000c   Data   RW         1929    .data               stm32h7xx_hal.o
    0x2000000c   0x08002918   0x00000008   Data   RW         2859    .data               system_stm32h7xx_dualcore_boot_cm4_cm7.o
    0x20000014   0x08002920   0x00000004   Data   RW         2963    .data               c_w.l(stdio_streams.o)
    0x20000018   0x08002924   0x00000004   Data   RW         2964    .data               c_w.l(stdio_streams.o)
    0x2000001c   0x08002928   0x00000004   Data   RW         2965    .data               c_w.l(stdio_streams.o)
    0x20000020        -       0x00000054   Zero   RW         2960    .bss                c_w.l(stdio_streams.o)
    0x20000074        -       0x00000054   Zero   RW         2961    .bss                c_w.l(stdio_streams.o)
    0x200000c8        -       0x00000054   Zero   RW         2962    .bss                c_w.l(stdio_streams.o)
    0x2000011c        -       0x00000060   Zero   RW         3028    .bss                c_w.l(libspace.o)
    0x2000017c   0x0800292c   0x00000004   PAD
    0x20000180        -       0x00000200   Zero   RW            2    HEAP                startup_stm32h745xx_cm7.o
    0x20000380        -       0x00000400   Zero   RW            1    STACK               startup_stm32h745xx_cm7.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1674        214          0          0          0    1264435   main_1.o
        64         26        664          0       1536        828   startup_stm32h745xx_cm7.o
       232         44          0         12          0      31072   stm32h7xx_hal.o
       328         28          0          0          0      40984   stm32h7xx_hal_cortex.o
        52         12          0          0          0       1207   stm32h7xx_hal_hsem.o
        28          6          0          0          0        914   stm32h7xx_hal_msp_1.o
       132          4          0          0          0        817   stm32h7xx_hal_pwr_ex.o
      2326         66          0          0          0       5264   stm32h7xx_hal_rcc.o
        20          0          0          0          0       4034   stm32h7xx_it_1.o
       248         40         16          8          0       1574   system_stm32h7xx_dualcore_boot_cm4_cm7.o

    ----------------------------------------------------------------------
      5110        <USER>        <GROUP>         20       1536    1351129   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       104          0          0          0          0         84   __printf.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        52         38          0          0          0         76   defsig_rtred_inner.o
        14          0          0          0          0         80   defsig_rtred_outer.o
        18          0          0          0          0         80   exit.o
        76          0          0          0          0         88   fclose.o
         8          0          0          0          0         68   ferror.o
       470          0          0          0          0         88   flsbuf.o
       236          4          0          0          0        128   fopen.o
        26          0          0          0          0         68   fputc.o
       248          6          0          0          0         84   fseek.o
        66          0          0          0          0         76   ftell.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
       312         46          0          0          0        112   initio.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         6          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         0          0          0          0          0          0   maybetermalloc1.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        70          0          0          0          0         80   setvbuf.o
       240          6          0          0          0        156   stdio.o
         0          0          0         12        252          0   stdio_streams.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
       102          0         12          0          0        240   sys_io.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o
         0          0          0          0          0          0   usenofp.o
       336         60          0          0          0        136   cosf.o
        38          0          0          0          0        116   fpclassifyf.o
        38         12          0          0          0        348   funder.o
       340         24         32          0          0        160   rredf.o
       404         60          0          0          0        212   sinf.o

    ----------------------------------------------------------------------
      4640        <USER>         <GROUP>         12        352       4936   Library Totals
        16          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      3458        212         12         12        348       3848   c_w.l
        10          0          0          0          0        116   fz_wv.l
      1156        156         32          0          0        972   m_wv.l

    ----------------------------------------------------------------------
      4640        <USER>         <GROUP>         12        352       4936   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9750        808        758         32       1888    1350337   Grand Totals
      9750        808        758         32       1888    1350337   ELF Image Totals
      9750        808        758         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10508 (  10.26kB)
    Total RW  Size (RW Data + ZI Data)              1920 (   1.88kB)
    Total ROM Size (Code + RO Data + RW Data)      10540 (  10.29kB)

==============================================================================

