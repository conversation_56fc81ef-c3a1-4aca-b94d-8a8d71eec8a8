# STM32H745 双仪表盘项目编译脚本
Write-Host "=== STM32H745 双仪表盘项目编译 ===" -ForegroundColor Green

# 设置工具链路径
$GCC_PATH = "C:\ST\STM32CubeCLT_1.18.0\GNU-tools-for-STM32\bin"
$CC = "$GCC_PATH\arm-none-eabi-gcc.exe"
$OBJCOPY = "$GCC_PATH\arm-none-eabi-objcopy.exe"
$SIZE = "$GCC_PATH\arm-none-eabi-size.exe"

Write-Host "编译器路径: $CC" -ForegroundColor Cyan

# 检查编译器是否存在
if (-not (Test-Path $CC)) {
    Write-Host "错误: 找不到ARM GCC编译器" -ForegroundColor Red
    exit 1
}

# 创建输出目录
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
}

Write-Host "正在编译双仪表盘项目..." -ForegroundColor Yellow

# 编译参数
$CFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb", 
    "-mfpu=fpv5-d16",
    "-mfloat-abi=hard",
    "-DUSE_HAL_DRIVER",
    "-DSTM32H745xx", 
    "-DCORE_CM7",
    "-ICore/Inc",
    "-I../Drivers/STM32H7xx_HAL_Driver/Inc",
    "-I../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy",
    "-I../Drivers/CMSIS/Device/ST/STM32H7xx/Include",
    "-I../Drivers/CMSIS/Include",
    "-Og", "-Wall", "-fdata-sections", "-ffunction-sections",
    "-g", "-gdwarf-2", "-MMD", "-MP"
)

# 链接参数
$LDFLAGS = @(
    "-mcpu=cortex-m7",
    "-mthumb",
    "-mfpu=fpv5-d16", 
    "-mfloat-abi=hard",
    "-specs=nano.specs",
    "-TSTM32H745XIHx_FLASH.ld",
    "-lc", "-lm", "-lnosys",
    "-Wl,-Map=build/LCD_Gauge.map,--cref",
    "-Wl,--gc-sections"
)

# 编译源文件
$sources = @(
    "Core/Src/main.c",
    "Core/Src/lcd.c", 
    "Core/Src/font.c",
    "Core/Src/gauge.c",
    "Core/Src/gauge_test.c",
    "Core/Src/stm32h7xx_it.c",
    "Core/Src/stm32h7xx_hal_msp.c"
)

$objects = @()

foreach ($source in $sources) {
    $basename = [System.IO.Path]::GetFileNameWithoutExtension($source)
    $object = "build/$basename.o"
    $objects += $object
    
    Write-Host "编译: $source" -ForegroundColor White
    & $CC $CFLAGS -c $source -o $object
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "编译失败: $source" -ForegroundColor Red
        exit 1
    }
}

# 编译HAL库关键文件
$hal_sources = @(
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c",
    "../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_sram.c",
    "../Drivers/CMSIS/Device/ST/STM32H7xx/Source/Templates/system_stm32h7xx.c"
)

foreach ($source in $hal_sources) {
    $basename = [System.IO.Path]::GetFileNameWithoutExtension($source)
    $object = "build/$basename.o"
    $objects += $object
    
    Write-Host "编译HAL: $([System.IO.Path]::GetFileName($source))" -ForegroundColor Gray
    & $CC $CFLAGS -c $source -o $object
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "编译失败: $source" -ForegroundColor Red
        exit 1
    }
}

# 编译启动文件
Write-Host "编译启动文件..." -ForegroundColor White
& $CC $CFLAGS -c "startup_stm32h745xx.s" -o "build/startup_stm32h745xx.o"
$objects += "build/startup_stm32h745xx.o"

if ($LASTEXITCODE -ne 0) {
    Write-Host "启动文件编译失败" -ForegroundColor Red
    exit 1
}

Write-Host "正在链接..." -ForegroundColor Yellow

# 链接所有目标文件
& $CC $LDFLAGS $objects -o "build/LCD_Gauge.elf"

if ($LASTEXITCODE -eq 0) {
    Write-Host "链接成功！" -ForegroundColor Green
    
    # 生成hex和bin文件
    & $OBJCOPY -O ihex "build/LCD_Gauge.elf" "build/LCD_Gauge.hex"
    & $OBJCOPY -O binary "build/LCD_Gauge.elf" "build/LCD_Gauge.bin"
    
    # 显示大小信息
    & $SIZE "build/LCD_Gauge.elf"
    
    Write-Host ""
    Write-Host "=== 编译完成 ===" -ForegroundColor Green
    Write-Host "HEX文件: build/LCD_Gauge.hex" -ForegroundColor Cyan
    Write-Host "BIN文件: build/LCD_Gauge.bin" -ForegroundColor Cyan
    Write-Host "ELF文件: build/LCD_Gauge.elf" -ForegroundColor Cyan
} else {
    Write-Host "链接失败！" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Ready to download to board..." -ForegroundColor Yellow
